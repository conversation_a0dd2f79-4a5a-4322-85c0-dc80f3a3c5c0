package com.example.utr_database.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GoAnnotationLast;

import java.util.List;

public interface GoAnnotationLastService {
    /**
     * 分页获取所有GO注释信息
     */
    IPage<GoAnnotationLast> getAllGoAnnotationLasts(Page<GoAnnotationLast> page);

    /**
     * 获取所有GO注释信息（不分页）
     */
    List<GoAnnotationLast> getAllGoAnnotationLasts();

    /**
     * 根据ID获取GO注释信息
     */
    GoAnnotationLast getGoAnnotationLastById(Integer id);

    /**
     * 根据goTerm获取所有相关的GO注释信息（核心功能）
     */
    List<GoAnnotationLast> getByGoTerm(String goTerm);

    /**
     * 根据geneSymbol获取GO注释信息
     */
    List<GoAnnotationLast> getByGeneSymbol(String geneSymbol);

    /**
     * 根据geneId获取GO注释信息
     */
    List<GoAnnotationLast> getByGeneId(String geneId);

    /**
     * 根据goId获取GO注释信息
     */
    List<GoAnnotationLast> getByGoId(String goId);

    /**
     * 根据goDomain获取GO注释信息
     */
    List<GoAnnotationLast> getByGoDomain(String goDomain);

    /**
     * 根据translationProjectIds获取GO注释信息
     */
    List<GoAnnotationLast> getByTranslationProjectIds(String translationProjectIds);

    /**
     * 分页搜索GO注释信息（全文搜索）
     */
    IPage<GoAnnotationLast> searchGoAnnotationLasts(String keyword, Page<GoAnnotationLast> page);

    /**
     * 搜索GO注释信息（全文搜索，不分页）
     */
    List<GoAnnotationLast> searchGoAnnotationLasts(String keyword);
}
