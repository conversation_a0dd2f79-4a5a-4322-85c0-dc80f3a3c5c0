package com.example.utr_database.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.TranscriptInfo;

import java.util.List;

public interface TranscriptInfoService {
    /**
     * 分页获取所有转录本信息
     */
    IPage<TranscriptInfo> getAllTranscriptInfo(Page<TranscriptInfo> page);

    /**
     * 获取所有转录本信息（不分页）
     */
    List<TranscriptInfo> getAllTranscriptInfo();

    /**
     * 根据转录本ID获取转录本信息
     */
    TranscriptInfo getTranscriptInfoById(String transcriptId);

    /**
     * 根据基因ID获取转录本信息
     */
    List<TranscriptInfo> getTranscriptInfoByGeneId(String geneId);

    /**
     * 根据基因符号获取转录本信息
     */
    List<TranscriptInfo> getTranscriptInfoByGeneSymbol(String geneSymbol);

    /**
     * 分页搜索转录本信息（根据转录本ID、基因ID、基因符号模糊搜索）
     */
    IPage<TranscriptInfo> searchTranscriptInfo(String keyword, Page<TranscriptInfo> page);

    /**
     * 搜索转录本信息（根据转录本ID、基因ID、基因符号模糊搜索）
     */
    List<TranscriptInfo> searchTranscriptInfo(String keyword);

    /**
     * 获取前50个唯一的transcriptId（按升序排列）
     */
    List<String> getTop50UniqueTranscriptIds();
} 