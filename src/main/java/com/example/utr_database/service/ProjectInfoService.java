package com.example.utr_database.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.utr_database.entity.ProjectInfo;

import java.util.List;

public interface ProjectInfoService extends IService<ProjectInfo> {
    
    /**
     * 模糊搜索项目信息
     * 
     * @param keyword 搜索关键词
     * @param current 当前页
     * @param size 每页大小
     * @return 分页后的搜索结果
     */
    Page<ProjectInfo> searchProjects(String keyword, long current, long size);
    
    /**
     * 模糊搜索项目信息（带排序功能）
     * 
     * @param keyword 搜索关键词
     * @param current 当前页
     * @param size 每页大小
     * @param sortField 排序字段
     * @param isAsc 是否升序
     * @return 分页后的搜索结果
     */
    Page<ProjectInfo> searchProjects(String keyword, long current, long size, String sortField, boolean isAsc);
    
    /**
     * 获取所有项目信息（分页）
     * 
     * @param current 当前页
     * @param size 每页大小
     * @return 分页后的项目列表
     */
    Page<ProjectInfo> listAllProjects(long current, long size);
    
    /**
     * 获取所有项目信息（分页带排序）
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param sortField 排序字段
     * @param isAsc 是否升序
     * @return 分页后的项目列表
     */
    Page<ProjectInfo> listAllProjects(long current, long size, String sortField, boolean isAsc);

    /**
     * 获取所有项目数据（不分页）
     */
    List<ProjectInfo> getAllProjects();

    /**
     * 根据关键词搜索所有项目数据（不分页）
     */
    List<ProjectInfo> searchAllProjects(String keyword);
} 