package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GoTermSummary;
import com.example.utr_database.mapper.GoTermSummaryMapper;
import com.example.utr_database.service.GoTermSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GoTermSummaryServiceImpl implements GoTermSummaryService {

    @Autowired
    private GoTermSummaryMapper goTermSummaryMapper;

    @Override
    public IPage<GoTermSummary> getAllGoTermSummaries(Page<GoTermSummary> page) {
        return goTermSummaryMapper.selectPage(page, null);
    }

    @Override
    public List<GoTermSummary> getAllGoTermSummaries() {
        return goTermSummaryMapper.selectList(null);
    }

    @Override
    public GoTermSummary getByGoTerm(String goTerm) {
        return goTermSummaryMapper.selectById(goTerm);
    }

    @Override
    public List<GoTermSummary> searchByGoTerm(String goTerm) {
        QueryWrapper<GoTermSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("goTerm", goTerm);
        return goTermSummaryMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoTermSummary> getByTissueCellType(String tissueCellType) {
        QueryWrapper<GoTermSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("tissueCellType", tissueCellType);
        return goTermSummaryMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoTermSummary> getByCellLine(String cellLine) {
        QueryWrapper<GoTermSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("cellLine", cellLine);
        return goTermSummaryMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoTermSummary> getByDisease(String disease) {
        QueryWrapper<GoTermSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("disease", disease);
        return goTermSummaryMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<GoTermSummary> searchGoTermSummaries(String keyword, Page<GoTermSummary> page) {
        QueryWrapper<GoTermSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("goTerm", keyword)
                .or().like("tissueCellType", keyword)
                .or().like("cellLine", keyword)
                .or().like("disease", keyword);
        return goTermSummaryMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<GoTermSummary> searchGoTermSummaries(String keyword) {
        QueryWrapper<GoTermSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("goTerm", keyword)
                .or().like("tissueCellType", keyword)
                .or().like("cellLine", keyword)
                .or().like("disease", keyword);
        return goTermSummaryMapper.selectList(queryWrapper);
    }

    @Override
    public List<String> getAllUniqueGoTerms() {
        // 使用QueryWrapper获取所有唯一的goTerm
        QueryWrapper<GoTermSummary> wrapper = new QueryWrapper<>();
        wrapper.select("goTerm") // 仅选择goTerm字段
              .orderBy(true, true, "goTerm"); // 按goTerm升序排列
        
        // 执行查询并转换结果
        List<GoTermSummary> goTermSummaryList = goTermSummaryMapper.selectList(wrapper);
        
        // 将GoTermSummary对象列表映射为goTerm字符串列表
        return goTermSummaryList.stream()
                         .map(GoTermSummary::getGoTerm)
                         .filter(goTerm -> goTerm != null && !goTerm.trim().isEmpty()) // 过滤空值
                         .collect(java.util.stream.Collectors.toList());
    }
} 