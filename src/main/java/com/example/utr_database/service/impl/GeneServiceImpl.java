package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.utr_database.entity.Gene;
import com.example.utr_database.mapper.GeneMapper;
import com.example.utr_database.service.GeneService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GeneServiceImpl extends ServiceImpl<GeneMapper, Gene> implements GeneService {
    
    @Override
    public Page<Gene> searchGenes(String keyword, long current, long size) {
        Page<Gene> page = new Page<>(current, size);
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            QueryWrapper<Gene> wrapper = new QueryWrapper<>();
            
            // 使用LIKE查询所有相关字段
            wrapper.like("geneSymbol", keyword)
                  .or().like("geneId", keyword)
                  .or().like("projectId", keyword)
                  .or().like("expressedTranscriptNumber", keyword)
                  .or().like("tissueCellType", keyword)
                  .or().like("cellLine", keyword)
                  .or().like("disease", keyword);
            
            return this.page(page, wrapper);
        } else {
            // 如果关键词为空，返回所有基因
            return this.page(page);
        }
    }
    
    @Override
    public Page<Gene> listAllGenes(long current, long size) {
        Page<Gene> page = new Page<>(current, size);
        return this.page(page);
    }
    
    @Override
    public Page<Gene> listAllGenes(long current, long size, String sortField, boolean isAsc) {
        Page<Gene> page = new Page<>(current, size);
        QueryWrapper<Gene> wrapper = new QueryWrapper<>();
        
        if (sortField != null && !sortField.isEmpty()) {
            wrapper.orderBy(true, isAsc, sortField);
        }
        
        return this.page(page, wrapper);
    }
    
    @Override
    public Page<Gene> searchGenes(String keyword, long current, long size, String sortField, boolean isAsc) {
        Page<Gene> page = new Page<>(current, size);
        
        QueryWrapper<Gene> wrapper = new QueryWrapper<>();
        
        // 添加搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            wrapper.like("geneSymbol", keyword)
                  .or().like("geneId", keyword)
                  .or().like("projectId", keyword)
                  .or().like("expressedTranscriptNumber", keyword)
                  .or().like("tissueCellType", keyword)
                  .or().like("cellLine", keyword)
                  .or().like("disease", keyword);
        }
        
        // 添加排序条件
        if (sortField != null && !sortField.isEmpty()) {
            wrapper.orderBy(true, isAsc, sortField);
        }
        
        return this.page(page, wrapper);
    }

    @Override
    public List<Gene> getAllGenes() {
        QueryWrapper<Gene> wrapper = new QueryWrapper<>();
        return this.list(wrapper);
    }

    @Override
    public List<Gene> searchAllGenes(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllGenes();
        }
        
        QueryWrapper<Gene> wrapper = new QueryWrapper<>();
        wrapper.like("geneSymbol", keyword)
              .or().like("geneId", keyword)
              .or().like("projectId", keyword)
              .or().like("expressedTranscriptNumber", keyword)
              .or().like("tissueCellType", keyword)
              .or().like("cellLine", keyword)
              .or().like("disease", keyword);
        
        return this.list(wrapper);
    }

    @Override
    public List<Gene> getGenesByGeneId(String geneId) {
        QueryWrapper<Gene> wrapper = new QueryWrapper<>();
        wrapper.eq("geneId", geneId);
        return this.list(wrapper);
    }

    @Override
    public List<Gene> getGenesByProjectId(String projectId) {
        QueryWrapper<Gene> wrapper = new QueryWrapper<>();
        wrapper.eq("projectId", projectId);
        return this.list(wrapper);
    }

    @Override
    public List<Gene> getGenesByGeneSymbol(String geneSymbol) {
        QueryWrapper<Gene> wrapper = new QueryWrapper<>();
        wrapper.eq("geneSymbol", geneSymbol);
        return this.list(wrapper);
    }

    @Override
    public List<Gene> getByMultipleConditions(List<String> geneIds, String tissueCellType, String cellLine, String disease, String chromosome) {
        QueryWrapper<Gene> wrapper = new QueryWrapper<>();

        // geneIds是必需条件
        if (geneIds != null && !geneIds.isEmpty()) {
            wrapper.in("geneId", geneIds);
        } else {
            // 如果没有提供geneIds，返回空列表
            return new java.util.ArrayList<>();
        }

        // 可选条件：tissueCellType
        if (tissueCellType != null && !tissueCellType.trim().isEmpty() && !"None".equalsIgnoreCase(tissueCellType.trim())) {
            wrapper.like("tissueCellType", tissueCellType);
        }

        // 可选条件：cellLine
        if (cellLine != null && !cellLine.trim().isEmpty() && !"None".equalsIgnoreCase(cellLine.trim())) {
            wrapper.like("cellLine", cellLine);
        }

        // 可选条件：disease
        if (disease != null && !disease.trim().isEmpty() && !"None".equalsIgnoreCase(disease.trim())) {
            wrapper.like("disease", disease);
        }

        // 可选条件：chromosome
        if (chromosome != null && !chromosome.trim().isEmpty() && !"None".equalsIgnoreCase(chromosome.trim())) {
            wrapper.like("chromosome", chromosome);
        }

        return this.list(wrapper);
    }
}