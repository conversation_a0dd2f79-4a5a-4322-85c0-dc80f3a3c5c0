package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.utr_database.entity.ProjectInfo;
import com.example.utr_database.mapper.ProjectInfoMapper;
import com.example.utr_database.service.ProjectInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProjectInfoServiceImpl extends ServiceImpl<ProjectInfoMapper, ProjectInfo> implements ProjectInfoService {
    
    @Override
    public Page<ProjectInfo> searchProjects(String keyword, long current, long size) {
        Page<ProjectInfo> page = new Page<>(current, size);
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            QueryWrapper<ProjectInfo> wrapper = new QueryWrapper<>();
            
            // 使用LIKE查询所有相关字段
            wrapper.like("projectId", keyword)
                  .or().like("bioProjectId", keyword)
                  .or().like("geoAccession", keyword)
                  .or().like("strategy", keyword)
                  .or().like("tissueOrCellType", keyword)
                  .or().like("cellLine", keyword)
                  .or().like("disease", keyword)
                  .or().like("srrNumber", keyword)
                  .or().like("pmid", keyword)
                  .or().like("translatedTranscriptsNumber", keyword);
            
            return this.page(page, wrapper);
        } else {
            // 如果关键词为空，返回所有项目
            return this.page(page);
        }
    }
    
    @Override
    public Page<ProjectInfo> listAllProjects(long current, long size) {
        Page<ProjectInfo> page = new Page<>(current, size);
        return this.page(page);
    }
    
    @Override
    public Page<ProjectInfo> listAllProjects(long current, long size, String sortField, boolean isAsc) {
        Page<ProjectInfo> page = new Page<>(current, size);
        QueryWrapper<ProjectInfo> wrapper = new QueryWrapper<>();
        
        if (sortField != null && !sortField.isEmpty()) {
            wrapper.orderBy(true, isAsc, sortField);
        }
        
        return this.page(page, wrapper);
    }
    
    @Override
    public Page<ProjectInfo> searchProjects(String keyword, long current, long size, String sortField, boolean isAsc) {
        Page<ProjectInfo> page = new Page<>(current, size);
        
        QueryWrapper<ProjectInfo> wrapper = new QueryWrapper<>();
        
        // 添加搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            wrapper.like("projectId", keyword)
                  .or().like("bioProjectId", keyword)
                  .or().like("geoAccession", keyword)
                  .or().like("strategy", keyword)
                  .or().like("tissueOrCellType", keyword)
                  .or().like("cellLine", keyword)
                  .or().like("disease", keyword)
                  .or().like("srrNumber", keyword)
                  .or().like("pmid", keyword)
                  .or().like("translatedTranscriptsNumber", keyword);
        }
        
        // 添加排序条件
        if (sortField != null && !sortField.isEmpty()) {
            wrapper.orderBy(true, isAsc, sortField);
        }
        
        return this.page(page, wrapper);
    }

    @Override
    public List<ProjectInfo> getAllProjects() {
        QueryWrapper<ProjectInfo> wrapper = new QueryWrapper<>();
        return this.list(wrapper);
    }

    @Override
    public List<ProjectInfo> searchAllProjects(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllProjects();
        }
        
        QueryWrapper<ProjectInfo> wrapper = new QueryWrapper<>();
        wrapper.like("projectId", keyword)
              .or().like("bioProjectId", keyword)
              .or().like("geoAccession", keyword)
              .or().like("strategy", keyword)
              .or().like("tissueOrCellType", keyword)
              .or().like("cellLine", keyword)
              .or().like("disease", keyword)
              .or().like("srrNumber", keyword)
              .or().like("pmid", keyword)
              .or().like("translatedTranscriptsNumber", keyword);
        
        return this.list(wrapper);
    }
} 