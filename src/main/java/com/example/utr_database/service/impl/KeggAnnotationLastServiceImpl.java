package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.KeggAnnotationLast;
import com.example.utr_database.mapper.KeggAnnotationLastMapper;
import com.example.utr_database.service.KeggAnnotationLastService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class KeggAnnotationLastServiceImpl implements KeggAnnotationLastService {

    @Autowired
    private KeggAnnotationLastMapper keggAnnotationLastMapper;

    @Override
    public IPage<KeggAnnotationLast> getAllKeggAnnotationLasts(Page<KeggAnnotationLast> page) {
        return keggAnnotationLastMapper.selectPage(page, null);
    }

    @Override
    public List<KeggAnnotationLast> getAllKeggAnnotationLasts() {
        return keggAnnotationLastMapper.selectList(null);
    }

    @Override
    public KeggAnnotationLast getKeggAnnotationLastById(Integer id) {
        return keggAnnotationLastMapper.selectById(id);
    }

    @Override
    public List<KeggAnnotationLast> getByPathwayDescription(String pathwayDescription) {
        QueryWrapper<KeggAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("pathwayDescription", pathwayDescription);
        return keggAnnotationLastMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggAnnotationLast> getByGeneSymbol(String geneSymbol) {
        QueryWrapper<KeggAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("geneSymbol", geneSymbol);
        return keggAnnotationLastMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggAnnotationLast> getByGeneId(String geneId) {
        QueryWrapper<KeggAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("geneId", geneId);
        return keggAnnotationLastMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggAnnotationLast> getByPathwayId(String pathwayId) {
        QueryWrapper<KeggAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pathwayId", pathwayId);
        return keggAnnotationLastMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggAnnotationLast> getByTranslationProjectIds(String translationProjectIds) {
        QueryWrapper<KeggAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("translationProjectIds", translationProjectIds);
        return keggAnnotationLastMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<KeggAnnotationLast> searchKeggAnnotationLasts(String keyword, Page<KeggAnnotationLast> page) {
        QueryWrapper<KeggAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("geneSymbol", keyword)
                .or().like("geneId", keyword)
                .or().like("pathwayDescription", keyword)
                .or().like("pathwayId", keyword)
                .or().like("translationProjectIds", keyword);
        return keggAnnotationLastMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<KeggAnnotationLast> searchKeggAnnotationLasts(String keyword) {
        QueryWrapper<KeggAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("geneSymbol", keyword)
                .or().like("geneId", keyword)
                .or().like("pathwayDescription", keyword)
                .or().like("pathwayId", keyword)
                .or().like("translationProjectIds", keyword);
        return keggAnnotationLastMapper.selectList(queryWrapper);
    }
}
