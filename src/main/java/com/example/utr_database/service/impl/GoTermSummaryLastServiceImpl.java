package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GoTermSummaryLast;
import com.example.utr_database.mapper.GoTermSummaryLastMapper;
import com.example.utr_database.service.GoTermSummaryLastService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GoTermSummaryLastServiceImpl implements GoTermSummaryLastService {

    @Autowired
    private GoTermSummaryLastMapper goTermSummaryLastMapper;

    @Override
    public IPage<GoTermSummaryLast> getAllGoTermSummaryLasts(Page<GoTermSummaryLast> page) {
        return goTermSummaryLastMapper.selectPage(page, null);
    }

    @Override
    public List<GoTermSummaryLast> getAllGoTermSummaryLasts() {
        return goTermSummaryLastMapper.selectList(null);
    }

    @Override
    public GoTermSummaryLast getByGoTerm(String goTerm) {
        return goTermSummaryLastMapper.selectById(goTerm);
    }

    @Override
    public List<GoTermSummaryLast> searchByGoTerm(String goTerm) {
        QueryWrapper<GoTermSummaryLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("goTerm", goTerm);
        return goTermSummaryLastMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoTermSummaryLast> getByTranslationProjectIds(String translationProjectIds) {
        QueryWrapper<GoTermSummaryLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("translationProjectIds", translationProjectIds);
        return goTermSummaryLastMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<GoTermSummaryLast> searchGoTermSummaryLasts(String keyword, Page<GoTermSummaryLast> page) {
        QueryWrapper<GoTermSummaryLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("goTerm", keyword)
                .or().like("translationProjectIds", keyword);
        return goTermSummaryLastMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<GoTermSummaryLast> searchGoTermSummaryLasts(String keyword) {
        QueryWrapper<GoTermSummaryLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("goTerm", keyword)
                .or().like("translationProjectIds", keyword);
        return goTermSummaryLastMapper.selectList(queryWrapper);
    }
}
