package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.KeggPathwaySummaryLast;
import com.example.utr_database.mapper.KeggPathwaySummaryLastMapper;
import com.example.utr_database.service.KeggPathwaySummaryLastService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class KeggPathwaySummaryLastServiceImpl implements KeggPathwaySummaryLastService {

    @Autowired
    private KeggPathwaySummaryLastMapper keggPathwaySummaryLastMapper;

    @Override
    public IPage<KeggPathwaySummaryLast> getAllKeggPathwaySummaryLasts(Page<KeggPathwaySummaryLast> page) {
        return keggPathwaySummaryLastMapper.selectPage(page, null);
    }

    @Override
    public List<KeggPathwaySummaryLast> getAllKeggPathwaySummaryLasts() {
        return keggPathwaySummaryLastMapper.selectList(null);
    }

    @Override
    public KeggPathwaySummaryLast getByPathwayDescription(String pathwayDescription) {
        return keggPathwaySummaryLastMapper.selectById(pathwayDescription);
    }

    @Override
    public List<KeggPathwaySummaryLast> searchByPathwayDescription(String pathwayDescription) {
        QueryWrapper<KeggPathwaySummaryLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("pathwayDescription", pathwayDescription);
        return keggPathwaySummaryLastMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggPathwaySummaryLast> getByTranslationProjectIds(String translationProjectIds) {
        QueryWrapper<KeggPathwaySummaryLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("translationProjectIds", translationProjectIds);
        return keggPathwaySummaryLastMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<KeggPathwaySummaryLast> searchKeggPathwaySummaryLasts(String keyword, Page<KeggPathwaySummaryLast> page) {
        QueryWrapper<KeggPathwaySummaryLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("pathwayDescription", keyword)
                .or().like("translationProjectIds", keyword);
        return keggPathwaySummaryLastMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<KeggPathwaySummaryLast> searchKeggPathwaySummaryLasts(String keyword) {
        QueryWrapper<KeggPathwaySummaryLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("pathwayDescription", keyword)
                .or().like("translationProjectIds", keyword);
        return keggPathwaySummaryLastMapper.selectList(queryWrapper);
    }
}
