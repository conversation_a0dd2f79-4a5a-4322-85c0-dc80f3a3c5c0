package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.utr_database.entity.ReferenceInfo;
import com.example.utr_database.mapper.ReferenceInfoMapper;
import com.example.utr_database.service.ReferenceInfoService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class ReferenceInfoServiceImpl extends ServiceImpl<ReferenceInfoMapper, ReferenceInfo> implements ReferenceInfoService {

    private static final List<String> LIKE_SEARCH_FIELDS = List.of(
            "bioProjectId", "geoAccession", "reference", "pubmedId", "doi"
    );

    @Override
    public Page<ReferenceInfo> listAllReferences(long current, long size) {
        Page<ReferenceInfo> page = new Page<>(current, size);
        QueryWrapper<ReferenceInfo> wrapper = new QueryWrapper<>();
        return this.page(page, wrapper);
    }

    @Override
    public Page<ReferenceInfo> search(Map<String, Object> searchParams, long current, long size) {
        Page<ReferenceInfo> page = new Page<>(current, size);
        QueryWrapper<ReferenceInfo> wrapper = new QueryWrapper<>();
        if (searchParams != null && !searchParams.isEmpty()) {
            wrapper.and(qw -> {
                for (Map.Entry<String, Object> entry : searchParams.entrySet()) {
                    String field = entry.getKey();
                    Object value = entry.getValue();
                    if (value != null && !value.toString().isEmpty() && LIKE_SEARCH_FIELDS.contains(field)) {
                        qw.or().like(field, value.toString());
                    }
                }
            });
        }
        return this.page(page, wrapper);
    }

    @Override
    public List<ReferenceInfo> getAllReferences() {
        QueryWrapper<ReferenceInfo> wrapper = new QueryWrapper<>();
        return this.list(wrapper);
    }

    @Override
    public List<ReferenceInfo> searchAllReferences(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllReferences();
        }
        
        QueryWrapper<ReferenceInfo> wrapper = new QueryWrapper<>();
        wrapper.and(qw -> {
            qw.like("bioProjectId", keyword)
              .or().like("geoAccession", keyword)
              .or().like("reference", keyword)
              .or().like("pubmedId", keyword)
              .or().like("doi", keyword);
        });
        return this.list(wrapper);
    }
} 