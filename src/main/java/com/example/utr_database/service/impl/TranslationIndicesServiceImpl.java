package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.utr_database.entity.TranslationIndices;
import com.example.utr_database.mapper.TranslationIndicesMapper;
import com.example.utr_database.service.TranslationIndicesService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TranslationIndicesServiceImpl extends ServiceImpl<TranslationIndicesMapper, TranslationIndices> implements TranslationIndicesService {
    
    @Override
    public Page<TranslationIndices> searchTranslationIndices(String keyword, long current, long size) {
        Page<TranslationIndices> page = new Page<>(current, size);
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            QueryWrapper<TranslationIndices> wrapper = new QueryWrapper<>();
            
            // 使用LIKE查询所有相关字段
            wrapper.like("transcriptId", keyword)
                  .or().like("projectId", keyword)
                  .or().like("bioprojectId", keyword)
                  .or().like("tissueCellType", keyword)
                  .or().like("cellLine", keyword)
                  .or().like("disease", keyword)
                  .or().like("tr", keyword)
                  .or().like("evi", keyword)
                  .or().like("te", keyword)
                  .or().like("geneId", keyword)
                  .or().like("geneSymbol", keyword)
                  .or().like("threeUtrComp", keyword)
                  .or().like("fiveUtrComp", keyword);
            
            return this.page(page, wrapper);
        } else {
            // 如果关键词为空，返回所有翻译指数
            return this.page(page);
        }
    }
    
    @Override
    public Page<TranslationIndices> listAllTranslationIndices(long current, long size) {
        Page<TranslationIndices> page = new Page<>(current, size);
        return this.page(page);
    }
    
    @Override
    public Page<TranslationIndices> listAllTranslationIndices(long current, long size, String sortField, boolean isAsc) {
        Page<TranslationIndices> page = new Page<>(current, size);
        QueryWrapper<TranslationIndices> wrapper = new QueryWrapper<>();
        
        if (sortField != null && !sortField.isEmpty()) {
            wrapper.orderBy(true, isAsc, sortField);
        }
        
        return this.page(page, wrapper);
    }
    
    @Override
    public Page<TranslationIndices> searchTranslationIndices(String keyword, long current, long size, String sortField, boolean isAsc) {
        Page<TranslationIndices> page = new Page<>(current, size);
        
        QueryWrapper<TranslationIndices> wrapper = new QueryWrapper<>();
        
        // 添加搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            wrapper.like("transcriptId", keyword)
                  .or().like("projectId", keyword)
                  .or().like("bioprojectId", keyword)
                  .or().like("tissueCellType", keyword)
                  .or().like("cellLine", keyword)
                  .or().like("disease", keyword)
                  .or().like("tr", keyword)
                  .or().like("evi", keyword)
                  .or().like("te", keyword)
                  .or().like("geneId", keyword)
                  .or().like("geneSymbol", keyword)
                  .or().like("threeUtrComp", keyword)
                  .or().like("fiveUtrComp", keyword);
        }
        
        // 添加排序条件
        if (sortField != null && !sortField.isEmpty()) {
            wrapper.orderBy(true, isAsc, sortField);
        }
        
        return this.page(page, wrapper);
    }

    @Override
    public List<TranslationIndices> getAllTranslationIndices() {
        QueryWrapper<TranslationIndices> wrapper = new QueryWrapper<>();
        return this.list(wrapper);
    }

    @Override
    public List<TranslationIndices> searchAllTranslationIndices(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllTranslationIndices();
        }
        
        QueryWrapper<TranslationIndices> wrapper = new QueryWrapper<>();
        wrapper.like("transcriptId", keyword)
              .or().like("projectId", keyword)
              .or().like("bioprojectId", keyword)
              .or().like("tissueCellType", keyword)
              .or().like("cellLine", keyword)
              .or().like("disease", keyword)
              .or().like("tr", keyword)
              .or().like("evi", keyword)
              .or().like("te", keyword)
              .or().like("geneId", keyword)
              .or().like("geneSymbol", keyword)
              .or().like("threeUtrComp", keyword)
              .or().like("fiveUtrComp", keyword);
        
        return this.list(wrapper);
    }

    @Override
    public List<TranslationIndices> getByTranscriptId(String transcriptId) {
        QueryWrapper<TranslationIndices> wrapper = new QueryWrapper<>();
        wrapper.eq("transcriptId", transcriptId);
        return this.list(wrapper);
    }

    @Override
    public List<TranslationIndices> getByProjectId(String projectId) {
        QueryWrapper<TranslationIndices> wrapper = new QueryWrapper<>();
        wrapper.eq("projectId", projectId);
        return this.list(wrapper);
    }

    @Override
    public List<TranslationIndices> getByBioprojectId(String bioprojectId) {
        QueryWrapper<TranslationIndices> wrapper = new QueryWrapper<>();
        wrapper.eq("bioprojectId", bioprojectId);
        return this.list(wrapper);
    }

    @Override
    public List<TranslationIndices> getByGeneId(String geneId) {
        QueryWrapper<TranslationIndices> wrapper = new QueryWrapper<>();
        wrapper.eq("geneId", geneId);
        return this.list(wrapper);
    }

    @Override
    public List<TranslationIndices> getByGeneSymbol(String geneSymbol) {
        QueryWrapper<TranslationIndices> wrapper = new QueryWrapper<>();
        wrapper.eq("geneSymbol", geneSymbol);
        return this.list(wrapper);
    }

    @Override
    public List<TranslationIndices> getByGeneIdAndProjectId(String geneId, String projectId) {
        QueryWrapper<TranslationIndices> wrapper = new QueryWrapper<>();
        wrapper.eq("geneId", geneId)
               .eq("projectId", projectId);
        return this.list(wrapper);
    }

    @Override
    public List<TranslationIndices> getByMultipleConditions(List<String> geneIds, String tissueCellType, String cellLine, String disease) {
        QueryWrapper<TranslationIndices> wrapper = new QueryWrapper<>();
        
        // geneIds是必需条件
        if (geneIds != null && !geneIds.isEmpty()) {
            wrapper.in("geneId", geneIds);
        } else {
            // 如果没有提供geneIds，返回空列表
            return new java.util.ArrayList<>();
        }
        
        // 可选条件：tissueCellType
        if (tissueCellType != null && !tissueCellType.trim().isEmpty() && !"None".equalsIgnoreCase(tissueCellType.trim())) {
            wrapper.like("tissueCellType", tissueCellType);
        }
        
        // 可选条件：cellLine
        if (cellLine != null && !cellLine.trim().isEmpty() && !"None".equalsIgnoreCase(cellLine.trim())) {
            wrapper.like("cellLine", cellLine);
        }
        
        // 可选条件：disease
        if (disease != null && !disease.trim().isEmpty() && !"None".equalsIgnoreCase(disease.trim())) {
            wrapper.like("disease", disease);
        }
        
        return this.list(wrapper);
    }
} 