package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GoAnnotation;
import com.example.utr_database.mapper.GoAnnotationMapper;
import com.example.utr_database.service.GoAnnotationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GoAnnotationServiceImpl implements GoAnnotationService {

    @Autowired
    private GoAnnotationMapper goAnnotationMapper;

    @Override
    public IPage<GoAnnotation> getAllGoAnnotations(Page<GoAnnotation> page) {
        return goAnnotationMapper.selectPage(page, null);
    }

    @Override
    public List<GoAnnotation> getAllGoAnnotations() {
        return goAnnotationMapper.selectList(null);
    }

    @Override
    public GoAnnotation getGoAnnotationById(Integer id) {
        return goAnnotationMapper.selectById(id);
    }

    @Override
    public List<GoAnnotation> getByGoTerm(String goTerm) {
        QueryWrapper<GoAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("goTerm", goTerm);
        return goAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoAnnotation> getByGeneSymbol(String geneSymbol) {
        QueryWrapper<GoAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("geneSymbol", geneSymbol);
        return goAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoAnnotation> getByGeneId(String geneId) {
        QueryWrapper<GoAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("geneId", geneId);
        return goAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoAnnotation> getByGoId(String goId) {
        QueryWrapper<GoAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("goId", goId);
        return goAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoAnnotation> getByGoDomain(String goDomain) {
        QueryWrapper<GoAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("goDomain", goDomain);
        return goAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<GoAnnotation> searchGoAnnotations(String keyword, Page<GoAnnotation> page) {
        QueryWrapper<GoAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("geneSymbol", keyword)
                .or().like("geneId", keyword)
                .or().like("goTerm", keyword)
                .or().like("goId", keyword)
                .or().like("goDomain", keyword)
                .or().like("tissueCellType", keyword)
                .or().like("cellLine", keyword)
                .or().like("disease", keyword);
        return goAnnotationMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<GoAnnotation> searchGoAnnotations(String keyword) {
        QueryWrapper<GoAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("geneSymbol", keyword)
                .or().like("geneId", keyword)
                .or().like("goTerm", keyword)
                .or().like("goId", keyword)
                .or().like("goDomain", keyword)
                .or().like("tissueCellType", keyword)
                .or().like("cellLine", keyword)
                .or().like("disease", keyword);
        return goAnnotationMapper.selectList(queryWrapper);
    }
} 