package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.utr_database.entity.GeneInfo;
import com.example.utr_database.mapper.GeneInfoMapper;
import com.example.utr_database.service.GeneInfoService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class GeneInfoServiceImpl extends ServiceImpl<GeneInfoMapper, GeneInfo> implements GeneInfoService {

    private static final Logger logger = LoggerFactory.getLogger(GeneInfoServiceImpl.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Page<GeneInfo> searchGeneInfo(String keyword, long current, long size) {
        Page<GeneInfo> page = new Page<>(current, size);
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            QueryWrapper<GeneInfo> wrapper = new QueryWrapper<>();
            
            // 使用LIKE查询所有相关字段
            wrapper.like("geneId", keyword)
                  .or().like("geneSymbol", keyword)
                  .or().like("approvedName", keyword)
                  .or().like("locusType", keyword)
                  .or().like("chromosome", keyword)
                  .or().like("transcriptCount", keyword);
            
            return this.page(page, wrapper);
        } else {
            // 如果关键词为空，返回所有基因信息
            return this.page(page);
        }
    }
    
    @Override
    public Page<GeneInfo> listAllGeneInfo(long current, long size) {
        Page<GeneInfo> page = new Page<>(current, size);
        return this.page(page);
    }
    
    @Override
    public Page<GeneInfo> listAllGeneInfo(long current, long size, String sortField, boolean isAsc) {
        Page<GeneInfo> page = new Page<>(current, size);
        QueryWrapper<GeneInfo> wrapper = new QueryWrapper<>();
        
        if (sortField != null && !sortField.isEmpty()) {
            wrapper.orderBy(true, isAsc, sortField);
        }
        
        return this.page(page, wrapper);
    }
    
    @Override
    public Page<GeneInfo> searchGeneInfo(String keyword, long current, long size, String sortField, boolean isAsc) {
        Page<GeneInfo> page = new Page<>(current, size);
        
        QueryWrapper<GeneInfo> wrapper = new QueryWrapper<>();
        
        // 添加搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            wrapper.like("geneId", keyword)
                  .or().like("geneSymbol", keyword)
                  .or().like("approvedName", keyword)
                  .or().like("locusType", keyword)
                  .or().like("chromosome", keyword)
                  .or().like("transcriptCount", keyword);
        }
        
        // 添加排序条件
        if (sortField != null && !sortField.isEmpty()) {
            wrapper.orderBy(true, isAsc, sortField);
        }
        
        return this.page(page, wrapper);
    }

    @Override
    public List<GeneInfo> getAllGeneInfo() {
        QueryWrapper<GeneInfo> wrapper = new QueryWrapper<>();
        return this.list(wrapper);
    }

    @Override
    public List<GeneInfo> searchAllGeneInfo(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllGeneInfo();
        }
        
        QueryWrapper<GeneInfo> wrapper = new QueryWrapper<>();
        wrapper.like("geneId", keyword)
              .or().like("geneSymbol", keyword)
              .or().like("approvedName", keyword)
              .or().like("locusType", keyword)
              .or().like("chromosome", keyword)
              .or().like("transcriptCount", keyword);
        
        return this.list(wrapper);
    }

    @Override
    public GeneInfo getByGeneId(String geneId) {
        return this.getById(geneId);
    }

    @Override
    public List<GeneInfo> getByGeneSymbol(String geneSymbol) {
        QueryWrapper<GeneInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("geneSymbol", geneSymbol);
        return this.list(wrapper);
    }

    @Override
    public List<GeneInfo> getByLocusType(String locusType) {
        QueryWrapper<GeneInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("locusType", locusType);
        return this.list(wrapper);
    }

    @Override
    public List<GeneInfo> getByTranscriptCountRange(Integer minCount, Integer maxCount) {
        QueryWrapper<GeneInfo> wrapper = new QueryWrapper<>();
        if (minCount != null) {
            wrapper.ge("transcriptCount", minCount);
        }
        if (maxCount != null) {
            wrapper.le("transcriptCount", maxCount);
        }
        return this.list(wrapper);
    }

    @Override
    public List<GeneInfo> getByGeneIdAndGeneSymbol(String geneId, String geneSymbol) {
        QueryWrapper<GeneInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("geneId", geneId)
               .eq("geneSymbol", geneSymbol);
        return this.list(wrapper);
    }

    @Override
    public List<String> getTop50UniqueGeneIds() {
        // 使用QueryWrapper获取前50个唯一的geneId并按升序排列
        QueryWrapper<GeneInfo> wrapper = new QueryWrapper<>();
        wrapper.select("geneId") // 仅选择geneId字段
              .groupBy("geneId") // 使用GROUP BY去重
              .orderBy(true, true, "geneId") // 按geneId升序排列
              .last("LIMIT 50"); // 限制返回前50条记录
        
        // 执行查询并转换结果
        List<GeneInfo> geneInfoList = this.list(wrapper);
        
        // 将GeneInfo对象列表映射为geneId字符串列表
        return geneInfoList.stream()
                         .map(geneInfo -> geneInfo.getGeneId())
                         .filter(geneId -> geneId != null && !geneId.trim().isEmpty()) // 过滤空值
                         .collect(java.util.stream.Collectors.toList());
    }

    @Override
    public List<String> getTop50UniqueGeneSymbols() {
        // 使用QueryWrapper获取前50个唯一的geneSymbol并按升序排列
        QueryWrapper<GeneInfo> wrapper = new QueryWrapper<>();
        wrapper.select("geneSymbol") // 仅选择geneSymbol字段
              .groupBy("geneSymbol") // 使用GROUP BY去重
              .orderBy(true, true, "geneSymbol") // 按geneSymbol升序排列
              .last("LIMIT 50"); // 限制返回前50条记录
        
        // 执行查询并转换结果
        List<GeneInfo> geneInfoList = this.list(wrapper);
        
        // 将GeneInfo对象列表映射为geneSymbol字符串列表
        return geneInfoList.stream()
                         .map(geneInfo -> geneInfo.getGeneSymbol())
                         .filter(geneSymbol -> geneSymbol != null && !geneSymbol.trim().isEmpty()) // 过滤空值
                         .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 将JSON字符串转换为Map对象（如果需要的话）
     */
    public Map<String, Object> parseTranscripts(String transcriptsJson) {
        if (transcriptsJson == null || transcriptsJson.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(transcriptsJson, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            logger.warn("解析transcripts JSON失败: {}", e.getMessage());
            return null;
        }
    }
} 