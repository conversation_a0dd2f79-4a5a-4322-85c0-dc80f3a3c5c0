package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.utr_database.entity.SampleInfo;
import com.example.utr_database.mapper.SampleInfoMapper;
import com.example.utr_database.service.SampleInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SampleInfoServiceImpl extends ServiceImpl<SampleInfoMapper, SampleInfo> implements SampleInfoService {
    
    private static final Logger logger = LoggerFactory.getLogger(SampleInfoServiceImpl.class);
    
    @Override
    public Page<SampleInfo> searchSamples(String keyword, long current, long size) {
        Page<SampleInfo> page = new Page<>(current, size);
        
        try {
            if (keyword != null && !keyword.trim().isEmpty()) {
                QueryWrapper<SampleInfo> wrapper = new QueryWrapper<>();
                
                // 使用LIKE查询替代FULLTEXT
                wrapper.like("sraAccession", keyword)
                      .or().like("projectId", keyword)
                      .or().like("geoAccession", keyword)
                      .or().like("bioProjectId", keyword)
                      .or().like("bioSampleId", keyword)
                      .or().like("cellLine", keyword)
                      .or().like("conditionField", keyword)
                      .or().like("strategy", keyword)
                      .or().like("platform", keyword)
                      .or().like("instrument", keyword)
                      .or().like("libraryLayout", keyword)
                      .or().like("tissueOrCellType", keyword)
                      .or().like("translatedTranscriptsNumber", keyword);
                
                return this.page(page, wrapper);
            } else {
                return this.page(page);
            }
        } catch (Exception e) {
            logger.error("搜索样本时出错: {}", e.getMessage(), e);
            return page;
        }
    }
    
    @Override
    public Page<SampleInfo> listAllSamples(long current, long size) {
        Page<SampleInfo> page = new Page<>(current, size);
        try {
            return this.page(page);
        } catch (Exception e) {
            logger.error("获取所有样本时出错: {}", e.getMessage(), e);
            return page;
        }
    }
    
    @Override
    public Page<SampleInfo> listAllSamples(long current, long size, String sortField, boolean isAsc) {
        Page<SampleInfo> page = new Page<>(current, size);
        try {
            QueryWrapper<SampleInfo> wrapper = new QueryWrapper<>();
            
            if (sortField != null && !sortField.isEmpty()) {
                wrapper.orderBy(true, isAsc, sortField);
            }
            
            return this.page(page, wrapper);
        } catch (Exception e) {
            logger.error("获取排序样本时出错: {}", e.getMessage(), e);
            return page;
        }
    }
    
    @Override
    public Page<SampleInfo> searchSamples(String keyword, long current, long size, String sortField, boolean isAsc) {
        Page<SampleInfo> page = new Page<>(current, size);
        
        try {
            QueryWrapper<SampleInfo> wrapper = new QueryWrapper<>();
            
            // 添加搜索条件
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.like("sraAccession", keyword)
                      .or().like("projectId", keyword)
                      .or().like("geoAccession", keyword)
                      .or().like("bioProjectId", keyword)
                      .or().like("bioSampleId", keyword)
                      .or().like("cellLine", keyword)
                      .or().like("conditionField", keyword)
                      .or().like("strategy", keyword)
                      .or().like("platform", keyword)
                      .or().like("instrument", keyword)
                      .or().like("libraryLayout", keyword)
                      .or().like("tissueOrCellType", keyword)
                      .or().like("translatedTranscriptsNumber", keyword);
            }
            
            // 添加排序条件
            if (sortField != null && !sortField.isEmpty()) {
                wrapper.orderBy(true, isAsc, sortField);
            }
            
            return this.page(page, wrapper);
        } catch (Exception e) {
            logger.error("搜索排序样本时出错: {}", e.getMessage(), e);
            return page;
        }
    }
    
    @Override
    public List<SampleInfo> getAllSamples() {
        try {
            QueryWrapper<SampleInfo> wrapper = new QueryWrapper<>();
            return this.list(wrapper);
        } catch (Exception e) {
            logger.error("获取所有样本数据时出错: {}", e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public List<SampleInfo> searchAllSamples(String keyword) {
        try {
            if (keyword == null || keyword.trim().isEmpty()) {
                return getAllSamples();
            }
            
            QueryWrapper<SampleInfo> wrapper = new QueryWrapper<>();
            wrapper.like("sraAccession", keyword)
                  .or().like("projectId", keyword)
                  .or().like("geoAccession", keyword)
                  .or().like("bioProjectId", keyword)
                  .or().like("bioSampleId", keyword)
                  .or().like("cellLine", keyword)
                  .or().like("conditionField", keyword)
                  .or().like("strategy", keyword)
                  .or().like("platform", keyword)
                  .or().like("instrument", keyword)
                  .or().like("libraryLayout", keyword)
                  .or().like("tissueOrCellType", keyword)
                  .or().like("translatedTranscriptsNumber", keyword);
            
            return this.list(wrapper);
        } catch (Exception e) {
            logger.error("搜索所有样本数据时出错: {}", e.getMessage(), e);
            return List.of();
        }
    }
}