package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.KeggPathwaySummary;
import com.example.utr_database.mapper.KeggPathwaySummaryMapper;
import com.example.utr_database.service.KeggPathwaySummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class KeggPathwaySummaryServiceImpl implements KeggPathwaySummaryService {

    @Autowired
    private KeggPathwaySummaryMapper keggPathwaySummaryMapper;

    @Override
    public IPage<KeggPathwaySummary> getAllKeggPathwaySummaries(Page<KeggPathwaySummary> page) {
        return keggPathwaySummaryMapper.selectPage(page, null);
    }

    @Override
    public List<KeggPathwaySummary> getAllKeggPathwaySummaries() {
        return keggPathwaySummaryMapper.selectList(null);
    }

    @Override
    public KeggPathwaySummary getByPathwayDescription(String pathwayDescription) {
        // 由于pathwayDescription是主键，直接使用selectById进行精确查询
        return keggPathwaySummaryMapper.selectById(pathwayDescription);
    }

    @Override
    public List<KeggPathwaySummary> searchByPathwayDescription(String pathwayDescription) {
        QueryWrapper<KeggPathwaySummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("pathwayDescription", pathwayDescription);
        return keggPathwaySummaryMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggPathwaySummary> getByTissueCellType(String tissueCellType) {
        QueryWrapper<KeggPathwaySummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("tissueCellType", tissueCellType);
        return keggPathwaySummaryMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggPathwaySummary> getByCellLine(String cellLine) {
        QueryWrapper<KeggPathwaySummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("cellLine", cellLine);
        return keggPathwaySummaryMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggPathwaySummary> getByDisease(String disease) {
        QueryWrapper<KeggPathwaySummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("disease", disease);
        return keggPathwaySummaryMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<KeggPathwaySummary> searchKeggPathwaySummaries(String keyword, Page<KeggPathwaySummary> page) {
        QueryWrapper<KeggPathwaySummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("pathwayDescription", keyword)
                .or().like("tissueCellType", keyword)
                .or().like("cellLine", keyword)
                .or().like("disease", keyword);
        return keggPathwaySummaryMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<KeggPathwaySummary> searchKeggPathwaySummaries(String keyword) {
        QueryWrapper<KeggPathwaySummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("pathwayDescription", keyword)
                .or().like("tissueCellType", keyword)
                .or().like("cellLine", keyword)
                .or().like("disease", keyword);
        return keggPathwaySummaryMapper.selectList(queryWrapper);
    }

    @Override
    public List<String> getAllUniquePathwayDescriptions() {
        // 使用QueryWrapper获取所有唯一的pathwayDescription
        QueryWrapper<KeggPathwaySummary> wrapper = new QueryWrapper<>();
        wrapper.select("pathwayDescription") // 仅选择pathwayDescription字段
              .orderBy(true, true, "pathwayDescription"); // 按pathwayDescription升序排列
        
        // 执行查询并转换结果
        List<KeggPathwaySummary> keggPathwaySummaryList = keggPathwaySummaryMapper.selectList(wrapper);
        
        // 将KeggPathwaySummary对象列表映射为pathwayDescription字符串列表
        return keggPathwaySummaryList.stream()
                         .map(KeggPathwaySummary::getPathwayDescription)
                         .filter(desc -> desc != null && !desc.trim().isEmpty()) // 过滤空值
                         .collect(java.util.stream.Collectors.toList());
    }

    @Override
    public boolean existsByPathwayDescription(String pathwayDescription) {
        // 检查指定的pathwayDescription是否存在
        KeggPathwaySummary result = keggPathwaySummaryMapper.selectById(pathwayDescription);
        return result != null;
    }
} 