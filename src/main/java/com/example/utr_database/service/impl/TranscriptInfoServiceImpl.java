package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.TranscriptInfo;
import com.example.utr_database.mapper.TranscriptInfoMapper;
import com.example.utr_database.service.TranscriptInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TranscriptInfoServiceImpl implements TranscriptInfoService {

    @Autowired
    private TranscriptInfoMapper transcriptInfoMapper;

    @Override
    public IPage<TranscriptInfo> getAllTranscriptInfo(Page<TranscriptInfo> page) {
        return transcriptInfoMapper.selectPage(page, null);
    }

    @Override
    public List<TranscriptInfo> getAllTranscriptInfo() {
        return transcriptInfoMapper.selectList(null);
    }

    @Override
    public TranscriptInfo getTranscriptInfoById(String transcriptId) {
        return transcriptInfoMapper.selectById(transcriptId);
    }

    @Override
    public List<TranscriptInfo> getTranscriptInfoByGeneId(String geneId) {
        QueryWrapper<TranscriptInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("geneId", geneId);
        return transcriptInfoMapper.selectList(queryWrapper);
    }

    @Override
    public List<TranscriptInfo> getTranscriptInfoByGeneSymbol(String geneSymbol) {
        QueryWrapper<TranscriptInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("geneSymbol", geneSymbol);
        return transcriptInfoMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<TranscriptInfo> searchTranscriptInfo(String keyword, Page<TranscriptInfo> page) {
        QueryWrapper<TranscriptInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("transcriptId", keyword)
                .or().like("geneId", keyword)
                .or().like("geneSymbol", keyword);
        return transcriptInfoMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<TranscriptInfo> searchTranscriptInfo(String keyword) {
        QueryWrapper<TranscriptInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("transcriptId", keyword)
                .or().like("geneId", keyword)
                .or().like("geneSymbol", keyword);
        return transcriptInfoMapper.selectList(queryWrapper);
    }

    @Override
    public List<String> getTop50UniqueTranscriptIds() {
        // 使用QueryWrapper获取前50个唯一的transcriptId并按升序排列
        QueryWrapper<TranscriptInfo> wrapper = new QueryWrapper<>();
        wrapper.select("transcriptId") // 仅选择transcriptId字段
              .groupBy("transcriptId") // 使用GROUP BY去重
              .orderBy(true, true, "transcriptId") // 按transcriptId升序排列
              .last("LIMIT 50"); // 限制返回前50条记录
        
        // 执行查询并转换结果
        List<TranscriptInfo> transcriptInfoList = transcriptInfoMapper.selectList(wrapper);
        
        // 将TranscriptInfo对象列表映射为transcriptId字符串列表
        return transcriptInfoList.stream()
                         .map(TranscriptInfo::getTranscriptId)
                         .filter(transcriptId -> transcriptId != null && !transcriptId.trim().isEmpty()) // 过滤空值
                         .collect(java.util.stream.Collectors.toList());
    }
} 