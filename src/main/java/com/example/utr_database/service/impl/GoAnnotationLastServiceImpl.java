package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GoAnnotationLast;
import com.example.utr_database.mapper.GoAnnotationLastMapper;
import com.example.utr_database.service.GoAnnotationLastService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GoAnnotationLastServiceImpl implements GoAnnotationLastService {

    @Autowired
    private GoAnnotationLastMapper goAnnotationLastMapper;

    @Override
    public IPage<GoAnnotationLast> getAllGoAnnotationLasts(Page<GoAnnotationLast> page) {
        return goAnnotationLastMapper.selectPage(page, null);
    }

    @Override
    public List<GoAnnotationLast> getAllGoAnnotationLasts() {
        return goAnnotationLastMapper.selectList(null);
    }

    @Override
    public GoAnnotationLast getGoAnnotationLastById(Integer id) {
        return goAnnotationLastMapper.selectById(id);
    }

    @Override
    public List<GoAnnotationLast> getByGoTerm(String goTerm) {
        QueryWrapper<GoAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("goTerm", goTerm);
        return goAnnotationLastMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoAnnotationLast> getByGeneSymbol(String geneSymbol) {
        QueryWrapper<GoAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("geneSymbol", geneSymbol);
        return goAnnotationLastMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoAnnotationLast> getByGeneId(String geneId) {
        QueryWrapper<GoAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("geneId", geneId);
        return goAnnotationLastMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoAnnotationLast> getByGoId(String goId) {
        QueryWrapper<GoAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("goId", goId);
        return goAnnotationLastMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoAnnotationLast> getByGoDomain(String goDomain) {
        QueryWrapper<GoAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("goDomain", goDomain);
        return goAnnotationLastMapper.selectList(queryWrapper);
    }

    @Override
    public List<GoAnnotationLast> getByTranslationProjectIds(String translationProjectIds) {
        QueryWrapper<GoAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("translationProjectIds", translationProjectIds);
        return goAnnotationLastMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<GoAnnotationLast> searchGoAnnotationLasts(String keyword, Page<GoAnnotationLast> page) {
        QueryWrapper<GoAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("geneSymbol", keyword)
                .or().like("geneId", keyword)
                .or().like("goTerm", keyword)
                .or().like("goId", keyword)
                .or().like("goDomain", keyword)
                .or().like("translationProjectIds", keyword);
        return goAnnotationLastMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<GoAnnotationLast> searchGoAnnotationLasts(String keyword) {
        QueryWrapper<GoAnnotationLast> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("geneSymbol", keyword)
                .or().like("geneId", keyword)
                .or().like("goTerm", keyword)
                .or().like("goId", keyword)
                .or().like("goDomain", keyword)
                .or().like("translationProjectIds", keyword);
        return goAnnotationLastMapper.selectList(queryWrapper);
    }
}
