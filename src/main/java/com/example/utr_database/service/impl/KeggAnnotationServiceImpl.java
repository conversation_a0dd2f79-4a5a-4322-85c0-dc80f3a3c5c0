package com.example.utr_database.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.KeggAnnotation;
import com.example.utr_database.mapper.KeggAnnotationMapper;
import com.example.utr_database.service.KeggAnnotationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class KeggAnnotationServiceImpl implements KeggAnnotationService {

    @Autowired
    private KeggAnnotationMapper keggAnnotationMapper;

    @Override
    public IPage<KeggAnnotation> getAllKeggAnnotations(Page<KeggAnnotation> page) {
        return keggAnnotationMapper.selectPage(page, null);
    }

    @Override
    public List<KeggAnnotation> getAllKeggAnnotations() {
        return keggAnnotationMapper.selectList(null);
    }

    @Override
    public KeggAnnotation getKeggAnnotationById(Integer id) {
        return keggAnnotationMapper.selectById(id);
    }

    @Override
    public List<KeggAnnotation> getByPathwayDescription(String pathwayDescription) {
        QueryWrapper<KeggAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("pathwayDescription", pathwayDescription);
        return keggAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggAnnotation> getByGeneSymbol(String geneSymbol) {
        QueryWrapper<KeggAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("geneSymbol", geneSymbol);
        return keggAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggAnnotation> getByGeneId(String geneId) {
        QueryWrapper<KeggAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("geneId", geneId);
        return keggAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggAnnotation> getByPathwayId(String pathwayId) {
        QueryWrapper<KeggAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pathwayId", pathwayId);
        return keggAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggAnnotation> getByTissueCellType(String tissueCellType) {
        QueryWrapper<KeggAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("tissueCellType", tissueCellType);
        return keggAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggAnnotation> getByCellLine(String cellLine) {
        QueryWrapper<KeggAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("cellLine", cellLine);
        return keggAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public List<KeggAnnotation> getByDisease(String disease) {
        QueryWrapper<KeggAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("disease", disease);
        return keggAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<KeggAnnotation> searchKeggAnnotations(String keyword, Page<KeggAnnotation> page) {
        QueryWrapper<KeggAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("geneSymbol", keyword)
                .or().like("geneId", keyword)
                .or().like("pathwayDescription", keyword)
                .or().like("pathwayId", keyword)
                .or().like("tissueCellType", keyword)
                .or().like("cellLine", keyword)
                .or().like("disease", keyword);
        return keggAnnotationMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<KeggAnnotation> searchKeggAnnotations(String keyword) {
        QueryWrapper<KeggAnnotation> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("geneSymbol", keyword)
                .or().like("geneId", keyword)
                .or().like("pathwayDescription", keyword)
                .or().like("pathwayId", keyword)
                .or().like("tissueCellType", keyword)
                .or().like("cellLine", keyword)
                .or().like("disease", keyword);
        return keggAnnotationMapper.selectList(queryWrapper);
    }

    @Override
    public List<String> getAllUniquePathwayDescriptions() {
        // 使用QueryWrapper获取所有唯一的pathwayDescription
        QueryWrapper<KeggAnnotation> wrapper = new QueryWrapper<>();
        wrapper.select("pathwayDescription") // 仅选择pathwayDescription字段
              .groupBy("pathwayDescription") // 使用GROUP BY去重
              .orderBy(true, true, "pathwayDescription"); // 按pathwayDescription升序排列
        
        // 执行查询并转换结果
        List<KeggAnnotation> keggAnnotationList = keggAnnotationMapper.selectList(wrapper);
        
        // 将KeggAnnotation对象列表映射为pathwayDescription字符串列表
        return keggAnnotationList.stream()
                         .map(KeggAnnotation::getPathwayDescription)
                         .filter(desc -> desc != null && !desc.trim().isEmpty()) // 过滤空值
                         .collect(java.util.stream.Collectors.toList());
    }

    @Override
    public List<String> getAllUniquePathwayIds() {
        // 使用QueryWrapper获取所有唯一的pathwayId
        QueryWrapper<KeggAnnotation> wrapper = new QueryWrapper<>();
        wrapper.select("pathwayId") // 仅选择pathwayId字段
              .groupBy("pathwayId") // 使用GROUP BY去重
              .orderBy(true, true, "pathwayId"); // 按pathwayId升序排列
        
        // 执行查询并转换结果
        List<KeggAnnotation> keggAnnotationList = keggAnnotationMapper.selectList(wrapper);
        
        // 将KeggAnnotation对象列表映射为pathwayId字符串列表
        return keggAnnotationList.stream()
                         .map(KeggAnnotation::getPathwayId)
                         .filter(pathwayId -> pathwayId != null && !pathwayId.trim().isEmpty()) // 过滤空值
                         .collect(java.util.stream.Collectors.toList());
    }
} 