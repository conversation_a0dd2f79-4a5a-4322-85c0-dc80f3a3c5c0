package com.example.utr_database.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.utr_database.entity.ReferenceInfo;

import java.util.List;
import java.util.Map;

public interface ReferenceInfoService extends IService<ReferenceInfo> {
    /**
     * 分页获取全部reference
     */
    Page<ReferenceInfo> listAllReferences(long current, long size);

    /**
     * 根据任意字段进行模糊搜索，分页
     */
    Page<ReferenceInfo> search(Map<String, Object> searchParams, long current, long size);

    /**
     * 获取所有reference数据（不分页）
     */
    List<ReferenceInfo> getAllReferences();

    /**
     * 根据关键词搜索所有reference数据（不分页）
     */
    List<ReferenceInfo> searchAllReferences(String keyword);
} 