package com.example.utr_database.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.utr_database.entity.Gene;

import java.util.List;

public interface GeneService extends IService<Gene> {
    
    /**
     * 模糊搜索基因信息
     * 
     * @param keyword 搜索关键词
     * @param current 当前页
     * @param size 每页大小
     * @return 分页后的搜索结果
     */
    Page<Gene> searchGenes(String keyword, long current, long size);
    
    /**
     * 模糊搜索基因信息（带排序功能）
     * 
     * @param keyword 搜索关键词
     * @param current 当前页
     * @param size 每页大小
     * @param sortField 排序字段
     * @param isAsc 是否升序
     * @return 分页后的搜索结果
     */
    Page<Gene> searchGenes(String keyword, long current, long size, String sortField, boolean isAsc);
    
    /**
     * 获取所有基因信息（分页）
     * 
     * @param current 当前页
     * @param size 每页大小
     * @return 分页后的基因列表
     */
    Page<Gene> listAllGenes(long current, long size);
    
    /**
     * 获取所有基因信息（分页带排序）
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param sortField 排序字段
     * @param isAsc 是否升序
     * @return 分页后的基因列表
     */
    Page<Gene> listAllGenes(long current, long size, String sortField, boolean isAsc);

    /**
     * 获取所有基因数据（不分页）
     */
    List<Gene> getAllGenes();

    /**
     * 根据关键词搜索所有基因数据（不分页）
     */
    List<Gene> searchAllGenes(String keyword);

    /**
     * 根据geneId查询基因信息
     */
    List<Gene> getGenesByGeneId(String geneId);

    /**
     * 根据projectId查询基因信息
     */
    List<Gene> getGenesByProjectId(String projectId);

    /**
     * 根据geneSymbol查询基因信息
     */
    List<Gene> getGenesByGeneSymbol(String geneSymbol);

    /**
     * 根据多个geneId和可选条件进行复合查询基因信息
     *
     * @param geneIds 基因ID列表（必需）
     * @param tissueCellType 组织细胞类型（可选）
     * @param cellLine 细胞系（可选）
     * @param disease 疾病（可选）
     * @param chromosome 染色体（可选）
     * @return 符合条件的基因信息列表
     */
    List<Gene> getByMultipleConditions(List<String> geneIds, String tissueCellType, String cellLine, String disease, String chromosome);
}