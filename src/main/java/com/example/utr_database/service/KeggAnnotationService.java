package com.example.utr_database.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.KeggAnnotation;

import java.util.List;

public interface KeggAnnotationService {
    /**
     * 分页获取所有KEGG注释信息
     */
    IPage<KeggAnnotation> getAllKeggAnnotations(Page<KeggAnnotation> page);

    /**
     * 获取所有KEGG注释信息（不分页）
     */
    List<KeggAnnotation> getAllKeggAnnotations();

    /**
     * 根据ID获取KEGG注释信息
     */
    KeggAnnotation getKeggAnnotationById(Integer id);

    /**
     * 根据pathwayDescription获取所有相关的KEGG注释信息（核心功能）
     */
    List<KeggAnnotation> getByPathwayDescription(String pathwayDescription);

    /**
     * 根据geneSymbol获取KEGG注释信息
     */
    List<KeggAnnotation> getByGeneSymbol(String geneSymbol);

    /**
     * 根据geneId获取KEGG注释信息
     */
    List<KeggAnnotation> getByGeneId(String geneId);

    /**
     * 根据pathwayId获取KEGG注释信息
     */
    List<KeggAnnotation> getByPathwayId(String pathwayId);

    /**
     * 根据tissueCellType获取KEGG注释信息
     */
    List<KeggAnnotation> getByTissueCellType(String tissueCellType);

    /**
     * 根据cellLine获取KEGG注释信息
     */
    List<KeggAnnotation> getByCellLine(String cellLine);

    /**
     * 根据disease获取KEGG注释信息
     */
    List<KeggAnnotation> getByDisease(String disease);

    /**
     * 分页搜索KEGG注释信息（全文搜索）
     */
    IPage<KeggAnnotation> searchKeggAnnotations(String keyword, Page<KeggAnnotation> page);

    /**
     * 搜索KEGG注释信息（全文搜索，不分页）
     */
    List<KeggAnnotation> searchKeggAnnotations(String keyword);

    /**
     * 获取所有唯一的pathwayDescription列表
     */
    List<String> getAllUniquePathwayDescriptions();

    /**
     * 获取所有唯一的pathwayId列表
     */
    List<String> getAllUniquePathwayIds();
} 