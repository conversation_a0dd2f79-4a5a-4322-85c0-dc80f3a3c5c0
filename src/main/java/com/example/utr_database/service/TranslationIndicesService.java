package com.example.utr_database.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.utr_database.entity.TranslationIndices;

import java.util.List;

public interface TranslationIndicesService extends IService<TranslationIndices> {
    
    /**
     * 模糊搜索翻译指数信息
     * 
     * @param keyword 搜索关键词
     * @param current 当前页
     * @param size 每页大小
     * @return 分页后的搜索结果
     */
    Page<TranslationIndices> searchTranslationIndices(String keyword, long current, long size);
    
    /**
     * 模糊搜索翻译指数信息（带排序功能）
     * 
     * @param keyword 搜索关键词
     * @param current 当前页
     * @param size 每页大小
     * @param sortField 排序字段
     * @param isAsc 是否升序
     * @return 分页后的搜索结果
     */
    Page<TranslationIndices> searchTranslationIndices(String keyword, long current, long size, String sortField, boolean isAsc);
    
    /**
     * 获取所有翻译指数信息（分页）
     * 
     * @param current 当前页
     * @param size 每页大小
     * @return 分页后的翻译指数列表
     */
    Page<TranslationIndices> listAllTranslationIndices(long current, long size);
    
    /**
     * 获取所有翻译指数信息（分页带排序）
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param sortField 排序字段
     * @param isAsc 是否升序
     * @return 分页后的翻译指数列表
     */
    Page<TranslationIndices> listAllTranslationIndices(long current, long size, String sortField, boolean isAsc);

    /**
     * 获取所有翻译指数数据（不分页）
     */
    List<TranslationIndices> getAllTranslationIndices();

    /**
     * 根据关键词搜索所有翻译指数数据（不分页）
     */
    List<TranslationIndices> searchAllTranslationIndices(String keyword);

    // 根据索引字段精确查询的方法
    
    /**
     * 根据transcriptId查询翻译指数信息
     */
    List<TranslationIndices> getByTranscriptId(String transcriptId);

    /**
     * 根据projectId查询翻译指数信息
     */
    List<TranslationIndices> getByProjectId(String projectId);

    /**
     * 根据bioprojectId查询翻译指数信息
     */
    List<TranslationIndices> getByBioprojectId(String bioprojectId);

    /**
     * 根据geneId查询翻译指数信息
     */
    List<TranslationIndices> getByGeneId(String geneId);

    /**
     * 根据geneSymbol查询翻译指数信息
     */
    List<TranslationIndices> getByGeneSymbol(String geneSymbol);

    /**
     * 根据geneId和projectId联合查询翻译指数信息
     */
    List<TranslationIndices> getByGeneIdAndProjectId(String geneId, String projectId);

    /**
     * 根据多个geneId和可选条件进行复合查询翻译指数信息
     * 
     * @param geneIds 基因ID列表（必需）
     * @param tissueCellType 组织细胞类型（可选）
     * @param cellLine 细胞系（可选）
     * @param disease 疾病（可选）
     * @return 符合条件的翻译指数信息列表
     */
    List<TranslationIndices> getByMultipleConditions(List<String> geneIds, String tissueCellType, String cellLine, String disease);
} 