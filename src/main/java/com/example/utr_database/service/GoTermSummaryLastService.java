package com.example.utr_database.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GoTermSummaryLast;

import java.util.List;

public interface GoTermSummaryLastService {
    /**
     * 分页获取所有GO术语摘要信息
     */
    IPage<GoTermSummaryLast> getAllGoTermSummaryLasts(Page<GoTermSummaryLast> page);

    /**
     * 获取所有GO术语摘要信息（不分页）
     */
    List<GoTermSummaryLast> getAllGoTermSummaryLasts();

    /**
     * 根据goTerm获取GO术语摘要信息（核心功能）
     */
    GoTermSummaryLast getByGoTerm(String goTerm);

    /**
     * 根据goTerm进行模糊搜索
     */
    List<GoTermSummaryLast> searchByGoTerm(String goTerm);

    /**
     * 根据translationProjectIds获取GO术语摘要信息
     */
    List<GoTermSummaryLast> getByTranslationProjectIds(String translationProjectIds);

    /**
     * 分页搜索GO术语摘要信息（全文搜索）
     */
    IPage<GoTermSummaryLast> searchGoTermSummaryLasts(String keyword, Page<GoTermSummaryLast> page);

    /**
     * 搜索GO术语摘要信息（全文搜索，不分页）
     */
    List<GoTermSummaryLast> searchGoTermSummaryLasts(String keyword);
}
