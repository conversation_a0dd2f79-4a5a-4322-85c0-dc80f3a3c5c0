package com.example.utr_database.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.KeggPathwaySummary;

import java.util.List;

public interface KeggPathwaySummaryService {
    /**
     * 分页获取所有KEGG通路摘要信息
     */
    IPage<KeggPathwaySummary> getAllKeggPathwaySummaries(Page<KeggPathwaySummary> page);

    /**
     * 获取所有KEGG通路摘要信息（不分页）
     */
    List<KeggPathwaySummary> getAllKeggPathwaySummaries();

    /**
     * 根据pathwayDescription精确获取KEGG通路摘要信息（核心功能 - 返回唯一数据）
     */
    KeggPathwaySummary getByPathwayDescription(String pathwayDescription);

    /**
     * 根据pathwayDescription模糊搜索KEGG通路摘要信息
     */
    List<KeggPathwaySummary> searchByPathwayDescription(String pathwayDescription);

    /**
     * 根据tissueCellType获取KEGG通路摘要信息
     */
    List<KeggPathwaySummary> getByTissueCellType(String tissueCellType);

    /**
     * 根据cellLine获取KEGG通路摘要信息
     */
    List<KeggPathwaySummary> getByCellLine(String cellLine);

    /**
     * 根据disease获取KEGG通路摘要信息
     */
    List<KeggPathwaySummary> getByDisease(String disease);

    /**
     * 分页搜索KEGG通路摘要信息（全文搜索）
     */
    IPage<KeggPathwaySummary> searchKeggPathwaySummaries(String keyword, Page<KeggPathwaySummary> page);

    /**
     * 搜索KEGG通路摘要信息（全文搜索，不分页）
     */
    List<KeggPathwaySummary> searchKeggPathwaySummaries(String keyword);

    /**
     * 获取所有唯一的pathwayDescription列表
     */
    List<String> getAllUniquePathwayDescriptions();

    /**
     * 检查pathwayDescription是否存在
     */
    boolean existsByPathwayDescription(String pathwayDescription);
} 