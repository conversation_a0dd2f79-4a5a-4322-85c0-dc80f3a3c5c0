package com.example.utr_database.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.KeggAnnotationLast;

import java.util.List;

public interface KeggAnnotationLastService {
    /**
     * 分页获取所有KEGG注释信息
     */
    IPage<KeggAnnotationLast> getAllKeggAnnotationLasts(Page<KeggAnnotationLast> page);

    /**
     * 获取所有KEGG注释信息（不分页）
     */
    List<KeggAnnotationLast> getAllKeggAnnotationLasts();

    /**
     * 根据ID获取KEGG注释信息
     */
    KeggAnnotationLast getKeggAnnotationLastById(Integer id);

    /**
     * 根据pathwayDescription获取所有相关的KEGG注释信息（核心功能）
     */
    List<KeggAnnotationLast> getByPathwayDescription(String pathwayDescription);

    /**
     * 根据geneSymbol获取KEGG注释信息
     */
    List<KeggAnnotationLast> getByGeneSymbol(String geneSymbol);

    /**
     * 根据geneId获取KEGG注释信息
     */
    List<KeggAnnotationLast> getByGeneId(String geneId);

    /**
     * 根据pathwayId获取KEGG注释信息
     */
    List<KeggAnnotationLast> getByPathwayId(String pathwayId);

    /**
     * 根据translationProjectIds获取KEGG注释信息
     */
    List<KeggAnnotationLast> getByTranslationProjectIds(String translationProjectIds);

    /**
     * 分页搜索KEGG注释信息（全文搜索）
     */
    IPage<KeggAnnotationLast> searchKeggAnnotationLasts(String keyword, Page<KeggAnnotationLast> page);

    /**
     * 搜索KEGG注释信息（全文搜索，不分页）
     */
    List<KeggAnnotationLast> searchKeggAnnotationLasts(String keyword);
}
