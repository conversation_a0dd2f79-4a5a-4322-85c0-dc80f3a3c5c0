package com.example.utr_database.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.utr_database.entity.SampleInfo;

import java.util.List;

public interface SampleInfoService extends IService<SampleInfo> {
    
    /**
     * 模糊搜索样本信息
     * 
     * @param keyword 搜索关键词
     * @param current 当前页
     * @param size 每页大小
     * @return 分页后的搜索结果
     */
    Page<SampleInfo> searchSamples(String keyword, long current, long size);
    
    /**
     * 模糊搜索样本信息（带排序功能）
     * 
     * @param keyword 搜索关键词
     * @param current 当前页
     * @param size 每页大小
     * @param sortField 排序字段
     * @param isAsc 是否升序
     * @return 分页后的搜索结果
     */
    Page<SampleInfo> searchSamples(String keyword, long current, long size, String sortField, boolean isAsc);
    
    /**
     * 获取所有样本信息（分页）
     * 
     * @param current 当前页
     * @param size 每页大小
     * @return 分页后的样本列表
     */
    Page<SampleInfo> listAllSamples(long current, long size);
    
    /**
     * 获取所有样本信息（分页带排序）
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param sortField 排序字段
     * @param isAsc 是否升序
     * @return 分页后的样本列表
     */
    Page<SampleInfo> listAllSamples(long current, long size, String sortField, boolean isAsc);

    /**
     * 获取所有样本数据（不分页）
     */
    List<SampleInfo> getAllSamples();

    /**
     * 根据关键词搜索所有样本数据（不分页）
     */
    List<SampleInfo> searchAllSamples(String keyword);
} 