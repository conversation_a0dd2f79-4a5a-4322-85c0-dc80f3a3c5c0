package com.example.utr_database.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GoAnnotation;

import java.util.List;

public interface GoAnnotationService {
    /**
     * 分页获取所有GO注释信息
     */
    IPage<GoAnnotation> getAllGoAnnotations(Page<GoAnnotation> page);

    /**
     * 获取所有GO注释信息（不分页）
     */
    List<GoAnnotation> getAllGoAnnotations();

    /**
     * 根据ID获取GO注释信息
     */
    GoAnnotation getGoAnnotationById(Integer id);

    /**
     * 根据goTerm获取所有相关的GO注释信息
     */
    List<GoAnnotation> getByGoTerm(String goTerm);

    /**
     * 根据geneSymbol获取GO注释信息
     */
    List<GoAnnotation> getByGeneSymbol(String geneSymbol);

    /**
     * 根据geneId获取GO注释信息
     */
    List<GoAnnotation> getByGeneId(String geneId);

    /**
     * 根据goId获取GO注释信息
     */
    List<GoAnnotation> getByGoId(String goId);

    /**
     * 根据goDomain获取GO注释信息
     */
    List<GoAnnotation> getByGoDomain(String goDomain);

    /**
     * 分页搜索GO注释信息（全文搜索）
     */
    IPage<GoAnnotation> searchGoAnnotations(String keyword, Page<GoAnnotation> page);

    /**
     * 搜索GO注释信息（全文搜索，不分页）
     */
    List<GoAnnotation> searchGoAnnotations(String keyword);
} 