package com.example.utr_database.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.utr_database.entity.GeneInfo;

import java.util.List;

public interface GeneInfoService extends IService<GeneInfo> {
    
    /**
     * 模糊搜索基因信息
     * 
     * @param keyword 搜索关键词
     * @param current 当前页
     * @param size 每页大小
     * @return 分页后的搜索结果
     */
    Page<GeneInfo> searchGeneInfo(String keyword, long current, long size);
    
    /**
     * 模糊搜索基因信息（带排序功能）
     * 
     * @param keyword 搜索关键词
     * @param current 当前页
     * @param size 每页大小
     * @param sortField 排序字段
     * @param isAsc 是否升序
     * @return 分页后的搜索结果
     */
    Page<GeneInfo> searchGeneInfo(String keyword, long current, long size, String sortField, boolean isAsc);
    
    /**
     * 获取所有基因信息（分页）
     * 
     * @param current 当前页
     * @param size 每页大小
     * @return 分页后的基因信息列表
     */
    Page<GeneInfo> listAllGeneInfo(long current, long size);
    
    /**
     * 获取所有基因信息（分页带排序）
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param sortField 排序字段
     * @param isAsc 是否升序
     * @return 分页后的基因信息列表
     */
    Page<GeneInfo> listAllGeneInfo(long current, long size, String sortField, boolean isAsc);

    /**
     * 获取所有基因信息数据（不分页）
     */
    List<GeneInfo> getAllGeneInfo();

    /**
     * 根据关键词搜索所有基因信息数据（不分页）
     */
    List<GeneInfo> searchAllGeneInfo(String keyword);

    /**
     * 根据geneId查询基因信息
     */
    GeneInfo getByGeneId(String geneId);

    /**
     * 根据geneSymbol查询基因信息
     */
    List<GeneInfo> getByGeneSymbol(String geneSymbol);

    /**
     * 根据locusType查询基因信息
     */
    List<GeneInfo> getByLocusType(String locusType);

    /**
     * 根据转录本数量范围查询基因信息
     */
    List<GeneInfo> getByTranscriptCountRange(Integer minCount, Integer maxCount);

    /**
     * 根据geneId和geneSymbol复合条件查询基因信息
     */
    List<GeneInfo> getByGeneIdAndGeneSymbol(String geneId, String geneSymbol);

    /**
     * 获取所有唯一的geneId列表（按升序排列）
     */
    List<String> getTop50UniqueGeneIds();

    /**
     * 获取所有唯一的geneSymbol列表（按升序排列）
     */
    List<String> getTop50UniqueGeneSymbols();
} 