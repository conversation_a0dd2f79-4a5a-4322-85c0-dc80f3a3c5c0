package com.example.utr_database.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.KeggPathwaySummaryLast;

import java.util.List;

public interface KeggPathwaySummaryLastService {
    /**
     * 分页获取所有KEGG通路摘要信息
     */
    IPage<KeggPathwaySummaryLast> getAllKeggPathwaySummaryLasts(Page<KeggPathwaySummaryLast> page);

    /**
     * 获取所有KEGG通路摘要信息（不分页）
     */
    List<KeggPathwaySummaryLast> getAllKeggPathwaySummaryLasts();

    /**
     * 根据pathwayDescription获取KEGG通路摘要信息（核心功能）
     */
    KeggPathwaySummaryLast getByPathwayDescription(String pathwayDescription);

    /**
     * 根据pathwayDescription进行模糊搜索
     */
    List<KeggPathwaySummaryLast> searchByPathwayDescription(String pathwayDescription);

    /**
     * 根据translationProjectIds获取KEGG通路摘要信息
     */
    List<KeggPathwaySummaryLast> getByTranslationProjectIds(String translationProjectIds);

    /**
     * 分页搜索KEGG通路摘要信息（全文搜索）
     */
    IPage<KeggPathwaySummaryLast> searchKeggPathwaySummaryLasts(String keyword, Page<KeggPathwaySummaryLast> page);

    /**
     * 搜索KEGG通路摘要信息（全文搜索，不分页）
     */
    List<KeggPathwaySummaryLast> searchKeggPathwaySummaryLasts(String keyword);
}
