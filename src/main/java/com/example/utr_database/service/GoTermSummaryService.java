package com.example.utr_database.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GoTermSummary;

import java.util.List;

public interface GoTermSummaryService {
    /**
     * 分页获取所有GO术语摘要信息
     */
    IPage<GoTermSummary> getAllGoTermSummaries(Page<GoTermSummary> page);

    /**
     * 获取所有GO术语摘要信息（不分页）
     */
    List<GoTermSummary> getAllGoTermSummaries();

    /**
     * 根据goTerm精确获取GO术语摘要信息（核心功能）
     */
    GoTermSummary getByGoTerm(String goTerm);

    /**
     * 根据goTerm模糊搜索GO术语摘要信息
     */
    List<GoTermSummary> searchByGoTerm(String goTerm);

    /**
     * 根据tissueCellType获取GO术语摘要信息
     */
    List<GoTermSummary> getByTissueCellType(String tissueCellType);

    /**
     * 根据cellLine获取GO术语摘要信息
     */
    List<GoTermSummary> getByCellLine(String cellLine);

    /**
     * 根据disease获取GO术语摘要信息
     */
    List<GoTermSummary> getByDisease(String disease);

    /**
     * 分页搜索GO术语摘要信息（全文搜索）
     */
    IPage<GoTermSummary> searchGoTermSummaries(String keyword, Page<GoTermSummary> page);

    /**
     * 搜索GO术语摘要信息（全文搜索，不分页）
     */
    List<GoTermSummary> searchGoTermSummaries(String keyword);

    /**
     * 获取所有唯一的goTerm列表
     */
    List<String> getAllUniqueGoTerms();
} 