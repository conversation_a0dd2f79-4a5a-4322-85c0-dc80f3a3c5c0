package com.example.utr_database.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.KeggPathwaySummaryLast;
import com.example.utr_database.service.KeggPathwaySummaryLastService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/keggpathwaysummarylast")
@CrossOrigin(origins = "*")
public class KeggPathwaySummaryLastController {

    @Autowired
    private KeggPathwaySummaryLastService keggPathwaySummaryLastService;

    /**
     * 分页获取所有KEGG通路摘要信息
     */
    @GetMapping
    public ResponseEntity<IPage<KeggPathwaySummaryLast>> getAllKeggPathwaySummaryLasts(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<KeggPathwaySummaryLast> pageObj = new Page<>(page, size);
        IPage<KeggPathwaySummaryLast> result = keggPathwaySummaryLastService.getAllKeggPathwaySummaryLasts(pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有KEGG通路摘要信息（不分页）
     */
    @GetMapping("/all")
    public ResponseEntity<List<KeggPathwaySummaryLast>> getAllKeggPathwaySummaryLasts() {
        List<KeggPathwaySummaryLast> result = keggPathwaySummaryLastService.getAllKeggPathwaySummaryLasts();
        return ResponseEntity.ok(result);
    }

    /**
     * 根据pathwayDescription获取KEGG通路摘要信息（核心功能）
     */
    @GetMapping("/pathway/{pathwayDescription}")
    public ResponseEntity<KeggPathwaySummaryLast> getByPathwayDescription(@PathVariable String pathwayDescription) {
        KeggPathwaySummaryLast keggPathwaySummaryLast = keggPathwaySummaryLastService.getByPathwayDescription(pathwayDescription);
        if (keggPathwaySummaryLast != null) {
            return ResponseEntity.ok(keggPathwaySummaryLast);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 根据pathwayDescription进行模糊搜索
     */
    @GetMapping("/search/pathway/{pathwayDescription}")
    public ResponseEntity<List<KeggPathwaySummaryLast>> searchByPathwayDescription(@PathVariable String pathwayDescription) {
        List<KeggPathwaySummaryLast> result = keggPathwaySummaryLastService.searchByPathwayDescription(pathwayDescription);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据translationProjectIds获取KEGG通路摘要信息
     */
    @GetMapping("/projectids/{translationProjectIds}")
    public ResponseEntity<List<KeggPathwaySummaryLast>> getByTranslationProjectIds(@PathVariable String translationProjectIds) {
        List<KeggPathwaySummaryLast> result = keggPathwaySummaryLastService.getByTranslationProjectIds(translationProjectIds);
        return ResponseEntity.ok(result);
    }

    /**
     * 分页搜索KEGG通路摘要信息
     */
    @GetMapping("/search")
    public ResponseEntity<IPage<KeggPathwaySummaryLast>> searchKeggPathwaySummaryLasts(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<KeggPathwaySummaryLast> pageObj = new Page<>(page, size);
        IPage<KeggPathwaySummaryLast> result = keggPathwaySummaryLastService.searchKeggPathwaySummaryLasts(keyword, pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 搜索KEGG通路摘要信息（不分页）
     */
    @GetMapping("/search/all")
    public ResponseEntity<List<KeggPathwaySummaryLast>> searchKeggPathwaySummaryLastsAll(@RequestParam String keyword) {
        List<KeggPathwaySummaryLast> result = keggPathwaySummaryLastService.searchKeggPathwaySummaryLasts(keyword);
        return ResponseEntity.ok(result);
    }
}
