package com.example.utr_database.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.KeggAnnotationLast;
import com.example.utr_database.service.KeggAnnotationLastService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/keggannotationlast")
@CrossOrigin(origins = "*")
public class KeggAnnotationLastController {

    @Autowired
    private KeggAnnotationLastService keggAnnotationLastService;

    /**
     * 分页获取所有KEGG注释信息
     */
    @GetMapping
    public ResponseEntity<IPage<KeggAnnotationLast>> getAllKeggAnnotationLasts(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<KeggAnnotationLast> pageObj = new Page<>(page, size);
        IPage<KeggAnnotationLast> result = keggAnnotationLastService.getAllKeggAnnotationLasts(pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有KEGG注释信息（不分页）
     */
    @GetMapping("/all")
    public ResponseEntity<List<KeggAnnotationLast>> getAllKeggAnnotationLasts() {
        List<KeggAnnotationLast> result = keggAnnotationLastService.getAllKeggAnnotationLasts();
        return ResponseEntity.ok(result);
    }

    /**
     * 根据ID获取KEGG注释信息
     */
    @GetMapping("/id/{id}")
    public ResponseEntity<KeggAnnotationLast> getKeggAnnotationLastById(@PathVariable Integer id) {
        KeggAnnotationLast keggAnnotationLast = keggAnnotationLastService.getKeggAnnotationLastById(id);
        if (keggAnnotationLast != null) {
            return ResponseEntity.ok(keggAnnotationLast);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 根据pathwayDescription获取所有相关的KEGG注释信息（核心功能）
     */
    @GetMapping("/pathway/{pathwayDescription}")
    public ResponseEntity<List<KeggAnnotationLast>> getByPathwayDescription(@PathVariable String pathwayDescription) {
        List<KeggAnnotationLast> result = keggAnnotationLastService.getByPathwayDescription(pathwayDescription);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据geneSymbol获取KEGG注释信息
     */
    @GetMapping("/genesymbol/{geneSymbol}")
    public ResponseEntity<List<KeggAnnotationLast>> getByGeneSymbol(@PathVariable String geneSymbol) {
        List<KeggAnnotationLast> result = keggAnnotationLastService.getByGeneSymbol(geneSymbol);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据geneId获取KEGG注释信息
     */
    @GetMapping("/geneid/{geneId}")
    public ResponseEntity<List<KeggAnnotationLast>> getByGeneId(@PathVariable String geneId) {
        List<KeggAnnotationLast> result = keggAnnotationLastService.getByGeneId(geneId);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据pathwayId获取KEGG注释信息
     */
    @GetMapping("/pathwayid/{pathwayId}")
    public ResponseEntity<List<KeggAnnotationLast>> getByPathwayId(@PathVariable String pathwayId) {
        List<KeggAnnotationLast> result = keggAnnotationLastService.getByPathwayId(pathwayId);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据translationProjectIds获取KEGG注释信息
     */
    @GetMapping("/projectids/{translationProjectIds}")
    public ResponseEntity<List<KeggAnnotationLast>> getByTranslationProjectIds(@PathVariable String translationProjectIds) {
        List<KeggAnnotationLast> result = keggAnnotationLastService.getByTranslationProjectIds(translationProjectIds);
        return ResponseEntity.ok(result);
    }

    /**
     * 分页搜索KEGG注释信息
     */
    @GetMapping("/search")
    public ResponseEntity<IPage<KeggAnnotationLast>> searchKeggAnnotationLasts(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<KeggAnnotationLast> pageObj = new Page<>(page, size);
        IPage<KeggAnnotationLast> result = keggAnnotationLastService.searchKeggAnnotationLasts(keyword, pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 搜索KEGG注释信息（不分页）
     */
    @GetMapping("/search/all")
    public ResponseEntity<List<KeggAnnotationLast>> searchKeggAnnotationLastsAll(@RequestParam String keyword) {
        List<KeggAnnotationLast> result = keggAnnotationLastService.searchKeggAnnotationLasts(keyword);
        return ResponseEntity.ok(result);
    }
}
