package com.example.utr_database.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.KeggAnnotation;
import com.example.utr_database.service.KeggAnnotationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/keggannotation")
@CrossOrigin(origins = "*")
public class KeggAnnotationController {

    @Autowired
    private KeggAnnotationService keggAnnotationService;

    /**
     * 分页获取所有KEGG注释信息
     */
    @GetMapping
    public ResponseEntity<IPage<KeggAnnotation>> getAllKeggAnnotations(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<KeggAnnotation> pageObj = new Page<>(page, size);
        IPage<KeggAnnotation> result = keggAnnotationService.getAllKeggAnnotations(pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有KEGG注释信息（不分页）
     */
    @GetMapping("/all")
    public ResponseEntity<List<KeggAnnotation>> getAllKeggAnnotations() {
        List<KeggAnnotation> result = keggAnnotationService.getAllKeggAnnotations();
        return ResponseEntity.ok(result);
    }

    /**
     * 根据ID获取KEGG注释信息
     */
    @GetMapping("/id/{id}")
    public ResponseEntity<KeggAnnotation> getKeggAnnotationById(@PathVariable Integer id) {
        KeggAnnotation keggAnnotation = keggAnnotationService.getKeggAnnotationById(id);
        if (keggAnnotation != null) {
            return ResponseEntity.ok(keggAnnotation);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 根据pathwayDescription获取所有相关的KEGG注释信息（核心功能）
     */
    @GetMapping("/pathway-description/{pathwayDescription}")
    public ResponseEntity<List<KeggAnnotation>> getByPathwayDescription(@PathVariable String pathwayDescription) {
        List<KeggAnnotation> result = keggAnnotationService.getByPathwayDescription(pathwayDescription);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据geneSymbol获取KEGG注释信息
     */
    @GetMapping("/genesymbol/{geneSymbol}")
    public ResponseEntity<List<KeggAnnotation>> getByGeneSymbol(@PathVariable String geneSymbol) {
        List<KeggAnnotation> result = keggAnnotationService.getByGeneSymbol(geneSymbol);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据geneId获取KEGG注释信息
     */
    @GetMapping("/geneid/{geneId}")
    public ResponseEntity<List<KeggAnnotation>> getByGeneId(@PathVariable String geneId) {
        List<KeggAnnotation> result = keggAnnotationService.getByGeneId(geneId);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据pathwayId获取KEGG注释信息
     */
    @GetMapping("/pathway-id/{pathwayId}")
    public ResponseEntity<List<KeggAnnotation>> getByPathwayId(@PathVariable String pathwayId) {
        List<KeggAnnotation> result = keggAnnotationService.getByPathwayId(pathwayId);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据tissueCellType获取KEGG注释信息
     */
    @GetMapping("/tissuecelltype/{tissueCellType}")
    public ResponseEntity<List<KeggAnnotation>> getByTissueCellType(@PathVariable String tissueCellType) {
        List<KeggAnnotation> result = keggAnnotationService.getByTissueCellType(tissueCellType);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据cellLine获取KEGG注释信息
     */
    @GetMapping("/cellline/{cellLine}")
    public ResponseEntity<List<KeggAnnotation>> getByCellLine(@PathVariable String cellLine) {
        List<KeggAnnotation> result = keggAnnotationService.getByCellLine(cellLine);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据disease获取KEGG注释信息
     */
    @GetMapping("/disease/{disease}")
    public ResponseEntity<List<KeggAnnotation>> getByDisease(@PathVariable String disease) {
        List<KeggAnnotation> result = keggAnnotationService.getByDisease(disease);
        return ResponseEntity.ok(result);
    }

    /**
     * 分页搜索KEGG注释信息
     */
    @GetMapping("/search")
    public ResponseEntity<IPage<KeggAnnotation>> searchKeggAnnotations(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<KeggAnnotation> pageObj = new Page<>(page, size);
        IPage<KeggAnnotation> result = keggAnnotationService.searchKeggAnnotations(keyword, pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 搜索KEGG注释信息（不分页）
     */
    @GetMapping("/search/all")
    public ResponseEntity<List<KeggAnnotation>> searchKeggAnnotationsAll(@RequestParam String keyword) {
        List<KeggAnnotation> result = keggAnnotationService.searchKeggAnnotations(keyword);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有唯一的pathwayDescription列表
     */
    @GetMapping("/all-pathway-descriptions")
    public ResponseEntity<List<String>> getAllUniquePathwayDescriptions() {
        List<String> result = keggAnnotationService.getAllUniquePathwayDescriptions();
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有唯一的pathwayId列表
     */
    @GetMapping("/all-pathway-ids")
    public ResponseEntity<List<String>> getAllUniquePathwayIds() {
        List<String> result = keggAnnotationService.getAllUniquePathwayIds();
        return ResponseEntity.ok(result);
    }
} 