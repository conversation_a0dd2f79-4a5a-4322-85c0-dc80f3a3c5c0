package com.example.utr_database.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.ReferenceInfo;
import com.example.utr_database.service.ReferenceInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/references")
@Tag(name = "参考文献信息", description = "referenceInfo相关操作")
public class ReferenceInfoController {

    @Autowired
    private ReferenceInfoService referenceInfoService;

    @GetMapping
    @Operation(
        summary = "获取所有reference",
        description = "分页获取所有reference信息",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "获取成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = Page.class))
            )
        }
    )
    public ResponseEntity<Page<ReferenceInfo>> getAllReferences(
        @Parameter(description = "当前页码，从1开始") @RequestParam(defaultValue = "1") long current,
        @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") long size
    ) {
        Page<ReferenceInfo> results = referenceInfoService.listAllReferences(current, size);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/all")
    @Operation(
        summary = "获取所有reference数据",
        description = "一次性获取所有reference信息，不分页",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "获取成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<ReferenceInfo>> getAllReferencesNoPaging() {
        List<ReferenceInfo> results = referenceInfoService.getAllReferences();
        return ResponseEntity.ok(results);
    }

    @GetMapping("/search")
    @Operation(
        summary = "搜索reference",
        description = "根据关键词进行模糊搜索，自动在bioProjectId、geoAccession、reference、pubmedId、doi字段中进行OR匹配",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "搜索成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = Page.class))
            )
        }
    )
    public ResponseEntity<Page<ReferenceInfo>> search(
        @Parameter(description = "搜索关键词") @RequestParam String keyword,
        @Parameter(description = "当前页码，从1开始") @RequestParam(defaultValue = "1") long current,
        @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") long size
    ) {
        Map<String, Object> searchParams = new HashMap<>();
        // 将关键词与所有可搜索字段关联起来，Service层会处理OR逻辑
        searchParams.put("bioProjectId", keyword);
        searchParams.put("geoAccession", keyword);
        searchParams.put("reference", keyword);
        searchParams.put("pubmedId", keyword);
        searchParams.put("doi", keyword);
        Page<ReferenceInfo> results = referenceInfoService.search(searchParams, current, size);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/search/all")
    @Operation(
        summary = "搜索所有reference数据",
        description = "根据关键词搜索所有reference信息，不分页",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "搜索成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<ReferenceInfo>> searchAllReferences(
        @Parameter(description = "搜索关键词") @RequestParam String keyword
    ) {
        List<ReferenceInfo> results = referenceInfoService.searchAllReferences(keyword);
        return ResponseEntity.ok(results);
    }
    
} 