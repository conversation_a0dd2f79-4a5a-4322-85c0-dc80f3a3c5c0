package com.example.utr_database.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.TranslationIndices;
import com.example.utr_database.service.TranslationIndicesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/translation-indices")
@Tag(name = "翻译指数信息", description = "翻译指数信息相关操作")
public class TranslationIndicesController {

    @Autowired
    private TranslationIndicesService translationIndicesService;
    
    @GetMapping
    @Operation(
        summary = "获取所有翻译指数",
        description = "分页获取所有翻译指数信息，支持排序",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "获取成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = Page.class))
            )
        }
    )
    public ResponseEntity<Page<TranslationIndices>> getAllTranslationIndices(
        @Parameter(description = "当前页码，从1开始") @RequestParam(defaultValue = "1") long current,
        @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") long size,
        @Parameter(description = "排序字段") @RequestParam(required = false) String sortField,
        @Parameter(description = "排序方式, true为升序, false为降序") @RequestParam(required = false, defaultValue = "true") boolean isAsc
    ) {
        Page<TranslationIndices> results;
        if (sortField != null && !sortField.isEmpty()) {
            results = translationIndicesService.listAllTranslationIndices(current, size, sortField, isAsc);
        } else {
            results = translationIndicesService.listAllTranslationIndices(current, size);
        }
        return ResponseEntity.ok(results);
    }

    @GetMapping("/all")
    @Operation(
        summary = "获取所有翻译指数数据",
        description = "一次性获取所有翻译指数信息，不分页",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "获取成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<TranslationIndices>> getAllTranslationIndicesNoPaging() {
        List<TranslationIndices> results = translationIndicesService.getAllTranslationIndices();
        return ResponseEntity.ok(results);
    }
    
    @GetMapping("/search")
    @Operation(
        summary = "搜索翻译指数",
        description = "根据关键词在transcriptId、projectId、bioprojectId、tissueCellType、cellLine、disease、tr、evi、te、geneId、geneSymbol、threeUtrComp、fiveUtrComp等字段中进行全文搜索，支持排序",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "搜索成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = Page.class))
            )
        }
    )
    public ResponseEntity<Page<TranslationIndices>> searchTranslationIndices(
        @Parameter(description = "搜索关键词") @RequestParam String keyword,
        @Parameter(description = "当前页码，从1开始") @RequestParam(defaultValue = "1") long current,
        @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") long size,
        @Parameter(description = "排序字段") @RequestParam(required = false) String sortField,
        @Parameter(description = "排序方式, true为升序, false为降序") @RequestParam(required = false, defaultValue = "true") boolean isAsc
    ) {
        Page<TranslationIndices> results;
        if (sortField != null && !sortField.isEmpty()) {
            results = translationIndicesService.searchTranslationIndices(keyword, current, size, sortField, isAsc);
        } else {
            results = translationIndicesService.searchTranslationIndices(keyword, current, size);
        }
        return ResponseEntity.ok(results);
    }

    @GetMapping("/search/all")
    @Operation(
        summary = "搜索所有翻译指数数据",
        description = "根据关键词搜索所有翻译指数信息，不分页",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "搜索成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<TranslationIndices>> searchAllTranslationIndices(
        @Parameter(description = "搜索关键词") @RequestParam String keyword
    ) {
        List<TranslationIndices> results = translationIndicesService.searchAllTranslationIndices(keyword);
        return ResponseEntity.ok(results);
    }

    // 根据索引字段精确查询的API端点

    @GetMapping("/transcriptId/{transcriptId}")
    @Operation(
        summary = "根据transcriptId查询翻译指数",
        description = "根据指定的transcriptId精确查询翻译指数信息",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<TranslationIndices>> getByTranscriptId(
        @Parameter(description = "转录本ID") @PathVariable String transcriptId
    ) {
        List<TranslationIndices> results = translationIndicesService.getByTranscriptId(transcriptId);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/projectId/{projectId}")
    @Operation(
        summary = "根据projectId查询翻译指数",
        description = "根据指定的projectId查询该项目下的所有翻译指数信息",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<TranslationIndices>> getByProjectId(
        @Parameter(description = "项目ID") @PathVariable String projectId
    ) {
        List<TranslationIndices> results = translationIndicesService.getByProjectId(projectId);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/bioprojectId/{bioprojectId}")
    @Operation(
        summary = "根据bioprojectId查询翻译指数",
        description = "根据指定的bioprojectId查询翻译指数信息",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<TranslationIndices>> getByBioprojectId(
        @Parameter(description = "生物项目ID") @PathVariable String bioprojectId
    ) {
        List<TranslationIndices> results = translationIndicesService.getByBioprojectId(bioprojectId);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/geneId/{geneId}")
    @Operation(
        summary = "根据geneId查询翻译指数",
        description = "根据指定的geneId查询翻译指数信息",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<TranslationIndices>> getByGeneId(
        @Parameter(description = "基因ID") @PathVariable String geneId
    ) {
        List<TranslationIndices> results = translationIndicesService.getByGeneId(geneId);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/geneSymbol/{geneSymbol}")
    @Operation(
        summary = "根据geneSymbol查询翻译指数",
        description = "根据指定的geneSymbol查询翻译指数信息",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<TranslationIndices>> getByGeneSymbol(
        @Parameter(description = "基因符号") @PathVariable String geneSymbol
    ) {
        List<TranslationIndices> results = translationIndicesService.getByGeneSymbol(geneSymbol);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/gene-project")
    @Operation(
        summary = "根据geneId和projectId联合查询翻译指数",
        description = "根据geneId和projectId共同筛选翻译指数信息",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<TranslationIndices>> getByGeneIdAndProjectId(
        @Parameter(description = "基因ID") @RequestParam String geneId,
        @Parameter(description = "项目ID") @RequestParam String projectId
    ) {
        List<TranslationIndices> results = translationIndicesService.getByGeneIdAndProjectId(geneId, projectId);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/filter")
    @Operation(
        summary = "根据多个条件进行复合查询翻译指数",
        description = "根据多个geneId（必需）和可选的tissueCellType、cellLine、disease条件筛选翻译指数信息。geneIds可以传入多个值，用逗号分隔。可选条件如果不需要筛选，可以省略参数、传递空值或传递'None'",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<TranslationIndices>> getByMultipleConditions(
        @Parameter(description = "基因ID列表，多个ID用逗号分隔", required = true) @RequestParam String geneIds,
        @Parameter(description = "组织细胞类型（可选，传递'None'表示不筛选此条件）") @RequestParam(required = false) String tissueCellType,
        @Parameter(description = "细胞系（可选，传递'None'表示不筛选此条件）") @RequestParam(required = false) String cellLine,
        @Parameter(description = "疾病（可选，传递'None'表示不筛选此条件）") @RequestParam(required = false) String disease
    ) {
        // 将逗号分隔的geneIds字符串转换为List
        List<String> geneIdList = java.util.Arrays.asList(geneIds.split(","))
                .stream()
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .collect(java.util.stream.Collectors.toList());
        
        List<TranslationIndices> results = translationIndicesService.getByMultipleConditions(
            geneIdList, tissueCellType, cellLine, disease
        );
        return ResponseEntity.ok(results);
    }
} 