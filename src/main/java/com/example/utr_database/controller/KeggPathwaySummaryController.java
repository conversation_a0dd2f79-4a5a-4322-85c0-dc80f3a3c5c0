package com.example.utr_database.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.KeggPathwaySummary;
import com.example.utr_database.service.KeggPathwaySummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/keggpathwaysummary")
@CrossOrigin(origins = "*")
public class KeggPathwaySummaryController {

    @Autowired
    private KeggPathwaySummaryService keggPathwaySummaryService;

    /**
     * 分页获取所有KEGG通路摘要信息
     */
    @GetMapping
    public ResponseEntity<IPage<KeggPathwaySummary>> getAllKeggPathwaySummaries(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<KeggPathwaySummary> pageObj = new Page<>(page, size);
        IPage<KeggPathwaySummary> result = keggPathwaySummaryService.getAllKeggPathwaySummaries(pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有KEGG通路摘要信息（不分页）
     */
    @GetMapping("/all")
    public ResponseEntity<List<KeggPathwaySummary>> getAllKeggPathwaySummaries() {
        List<KeggPathwaySummary> result = keggPathwaySummaryService.getAllKeggPathwaySummaries();
        return ResponseEntity.ok(result);
    }

    /**
     * 根据pathwayDescription模糊搜索KEGG通路摘要信息
     * <p>
     * 说明：原先接口使用主键精确查询，为了提高可用性，这里改为 <code>LIKE</code> 模糊查询，
     * 返回所有满足条件的记录。
     */
    @GetMapping("/pathway-description/{pathwayDescription}")
    public ResponseEntity<List<KeggPathwaySummary>> getByPathwayDescription(@PathVariable String pathwayDescription) {
        List<KeggPathwaySummary> result = keggPathwaySummaryService.searchByPathwayDescription(pathwayDescription);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据pathwayDescription模糊搜索KEGG通路摘要信息
     */
    @GetMapping("/pathway-description/search/{pathwayDescription}")
    public ResponseEntity<List<KeggPathwaySummary>> searchByPathwayDescription(@PathVariable String pathwayDescription) {
        List<KeggPathwaySummary> result = keggPathwaySummaryService.searchByPathwayDescription(pathwayDescription);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据tissueCellType获取KEGG通路摘要信息
     */
    @GetMapping("/tissuecelltype/{tissueCellType}")
    public ResponseEntity<List<KeggPathwaySummary>> getByTissueCellType(@PathVariable String tissueCellType) {
        List<KeggPathwaySummary> result = keggPathwaySummaryService.getByTissueCellType(tissueCellType);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据cellLine获取KEGG通路摘要信息
     */
    @GetMapping("/cellline/{cellLine}")
    public ResponseEntity<List<KeggPathwaySummary>> getByCellLine(@PathVariable String cellLine) {
        List<KeggPathwaySummary> result = keggPathwaySummaryService.getByCellLine(cellLine);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据disease获取KEGG通路摘要信息
     */
    @GetMapping("/disease/{disease}")
    public ResponseEntity<List<KeggPathwaySummary>> getByDisease(@PathVariable String disease) {
        List<KeggPathwaySummary> result = keggPathwaySummaryService.getByDisease(disease);
        return ResponseEntity.ok(result);
    }

    /**
     * 分页搜索KEGG通路摘要信息
     */
    @GetMapping("/search")
    public ResponseEntity<IPage<KeggPathwaySummary>> searchKeggPathwaySummaries(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<KeggPathwaySummary> pageObj = new Page<>(page, size);
        IPage<KeggPathwaySummary> result = keggPathwaySummaryService.searchKeggPathwaySummaries(keyword, pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 搜索KEGG通路摘要信息（不分页）
     */
    @GetMapping("/search/all")
    public ResponseEntity<List<KeggPathwaySummary>> searchKeggPathwaySummariesAll(@RequestParam String keyword) {
        List<KeggPathwaySummary> result = keggPathwaySummaryService.searchKeggPathwaySummaries(keyword);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有唯一的pathwayDescription列表
     */
    @GetMapping("/all-pathway-descriptions")
    public ResponseEntity<List<String>> getAllUniquePathwayDescriptions() {
        List<String> result = keggPathwaySummaryService.getAllUniquePathwayDescriptions();
        return ResponseEntity.ok(result);
    }

    /**
     * 检查pathwayDescription是否存在
     */
    @GetMapping("/exists/{pathwayDescription}")
    public ResponseEntity<Boolean> existsByPathwayDescription(@PathVariable String pathwayDescription) {
        boolean exists = keggPathwaySummaryService.existsByPathwayDescription(pathwayDescription);
        return ResponseEntity.ok(exists);
    }
} 