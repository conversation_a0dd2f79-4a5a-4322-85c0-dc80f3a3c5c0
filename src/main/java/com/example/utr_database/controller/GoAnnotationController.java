package com.example.utr_database.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GoAnnotation;
import com.example.utr_database.service.GoAnnotationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/goannotation")
@CrossOrigin(origins = "*")
public class GoAnnotationController {

    @Autowired
    private GoAnnotationService goAnnotationService;

    /**
     * 分页获取所有GO注释信息
     */
    @GetMapping
    public ResponseEntity<IPage<GoAnnotation>> getAllGoAnnotations(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<GoAnnotation> pageObj = new Page<>(page, size);
        IPage<GoAnnotation> result = goAnnotationService.getAllGoAnnotations(pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有GO注释信息（不分页）
     */
    @GetMapping("/all")
    public ResponseEntity<List<GoAnnotation>> getAllGoAnnotations() {
        List<GoAnnotation> result = goAnnotationService.getAllGoAnnotations();
        return ResponseEntity.ok(result);
    }

    /**
     * 根据ID获取GO注释信息
     */
    @GetMapping("/id/{id}")
    public ResponseEntity<GoAnnotation> getGoAnnotationById(@PathVariable Integer id) {
        GoAnnotation goAnnotation = goAnnotationService.getGoAnnotationById(id);
        if (goAnnotation != null) {
            return ResponseEntity.ok(goAnnotation);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 根据goTerm获取所有相关的GO注释信息（核心功能）
     */
    @GetMapping("/goterm/{goTerm}")
    public ResponseEntity<List<GoAnnotation>> getByGoTerm(@PathVariable String goTerm) {
        List<GoAnnotation> result = goAnnotationService.getByGoTerm(goTerm);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据geneSymbol获取GO注释信息
     */
    @GetMapping("/genesymbol/{geneSymbol}")
    public ResponseEntity<List<GoAnnotation>> getByGeneSymbol(@PathVariable String geneSymbol) {
        List<GoAnnotation> result = goAnnotationService.getByGeneSymbol(geneSymbol);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据geneId获取GO注释信息
     */
    @GetMapping("/geneid/{geneId}")
    public ResponseEntity<List<GoAnnotation>> getByGeneId(@PathVariable String geneId) {
        List<GoAnnotation> result = goAnnotationService.getByGeneId(geneId);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据goId获取GO注释信息
     */
    @GetMapping("/goid/{goId}")
    public ResponseEntity<List<GoAnnotation>> getByGoId(@PathVariable String goId) {
        List<GoAnnotation> result = goAnnotationService.getByGoId(goId);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据goDomain获取GO注释信息
     */
    @GetMapping("/godomain/{goDomain}")
    public ResponseEntity<List<GoAnnotation>> getByGoDomain(@PathVariable String goDomain) {
        List<GoAnnotation> result = goAnnotationService.getByGoDomain(goDomain);
        return ResponseEntity.ok(result);
    }

    /**
     * 分页搜索GO注释信息
     */
    @GetMapping("/search")
    public ResponseEntity<IPage<GoAnnotation>> searchGoAnnotations(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<GoAnnotation> pageObj = new Page<>(page, size);
        IPage<GoAnnotation> result = goAnnotationService.searchGoAnnotations(keyword, pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 搜索GO注释信息（不分页）
     */
    @GetMapping("/search/all")
    public ResponseEntity<List<GoAnnotation>> searchGoAnnotationsAll(@RequestParam String keyword) {
        List<GoAnnotation> result = goAnnotationService.searchGoAnnotations(keyword);
        return ResponseEntity.ok(result);
    }
} 