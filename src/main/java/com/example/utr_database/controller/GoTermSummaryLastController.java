package com.example.utr_database.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GoTermSummaryLast;
import com.example.utr_database.service.GoTermSummaryLastService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/gotermsummarylast")
@CrossOrigin(origins = "*")
public class GoTermSummaryLastController {

    @Autowired
    private GoTermSummaryLastService goTermSummaryLastService;

    /**
     * 分页获取所有GO术语摘要信息
     */
    @GetMapping
    public ResponseEntity<IPage<GoTermSummaryLast>> getAllGoTermSummaryLasts(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<GoTermSummaryLast> pageObj = new Page<>(page, size);
        IPage<GoTermSummaryLast> result = goTermSummaryLastService.getAllGoTermSummaryLasts(pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有GO术语摘要信息（不分页）
     */
    @GetMapping("/all")
    public ResponseEntity<List<GoTermSummaryLast>> getAllGoTermSummaryLasts() {
        List<GoTermSummaryLast> result = goTermSummaryLastService.getAllGoTermSummaryLasts();
        return ResponseEntity.ok(result);
    }

    /**
     * 根据goTerm获取GO术语摘要信息（核心功能）
     */
    @GetMapping("/goterm/{goTerm}")
    public ResponseEntity<GoTermSummaryLast> getByGoTerm(@PathVariable String goTerm) {
        GoTermSummaryLast goTermSummaryLast = goTermSummaryLastService.getByGoTerm(goTerm);
        if (goTermSummaryLast != null) {
            return ResponseEntity.ok(goTermSummaryLast);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 根据goTerm进行模糊搜索
     */
    @GetMapping("/search/goterm/{goTerm}")
    public ResponseEntity<List<GoTermSummaryLast>> searchByGoTerm(@PathVariable String goTerm) {
        List<GoTermSummaryLast> result = goTermSummaryLastService.searchByGoTerm(goTerm);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据translationProjectIds获取GO术语摘要信息
     */
    @GetMapping("/projectids/{translationProjectIds}")
    public ResponseEntity<List<GoTermSummaryLast>> getByTranslationProjectIds(@PathVariable String translationProjectIds) {
        List<GoTermSummaryLast> result = goTermSummaryLastService.getByTranslationProjectIds(translationProjectIds);
        return ResponseEntity.ok(result);
    }

    /**
     * 分页搜索GO术语摘要信息
     */
    @GetMapping("/search")
    public ResponseEntity<IPage<GoTermSummaryLast>> searchGoTermSummaryLasts(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<GoTermSummaryLast> pageObj = new Page<>(page, size);
        IPage<GoTermSummaryLast> result = goTermSummaryLastService.searchGoTermSummaryLasts(keyword, pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 搜索GO术语摘要信息（不分页）
     */
    @GetMapping("/search/all")
    public ResponseEntity<List<GoTermSummaryLast>> searchGoTermSummaryLastsAll(@RequestParam String keyword) {
        List<GoTermSummaryLast> result = goTermSummaryLastService.searchGoTermSummaryLasts(keyword);
        return ResponseEntity.ok(result);
    }
}
