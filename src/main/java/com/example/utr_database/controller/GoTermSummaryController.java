package com.example.utr_database.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GoTermSummary;
import com.example.utr_database.service.GoTermSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/gotermsummary")
@CrossOrigin(origins = "*")
public class GoTermSummaryController {

    @Autowired
    private GoTermSummaryService goTermSummaryService;

    /**
     * 分页获取所有GO术语摘要信息
     */
    @GetMapping
    public ResponseEntity<IPage<GoTermSummary>> getAllGoTermSummaries(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<GoTermSummary> pageObj = new Page<>(page, size);
        IPage<GoTermSummary> result = goTermSummaryService.getAllGoTermSummaries(pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有GO术语摘要信息（不分页）
     */
    @GetMapping("/all")
    public ResponseEntity<List<GoTermSummary>> getAllGoTermSummaries() {
        List<GoTermSummary> result = goTermSummaryService.getAllGoTermSummaries();
        return ResponseEntity.ok(result);
    }

    /**
     * 根据goTerm模糊搜索GO术语摘要信息
     * <p>
     * 与 <code>/goterm/search/{goTerm}</code> 功能一致，为了简化前端调用，
     * 直接在此处使用 <code>LIKE</code> 查询并返回匹配列表。
     */
    @GetMapping("/goterm/{goTerm}")
    public ResponseEntity<List<GoTermSummary>> getByGoTerm(@PathVariable String goTerm) {
        List<GoTermSummary> result = goTermSummaryService.searchByGoTerm(goTerm);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据goTerm模糊搜索GO术语摘要信息
     */
    @GetMapping("/goterm/search/{goTerm}")
    public ResponseEntity<List<GoTermSummary>> searchByGoTerm(@PathVariable String goTerm) {
        List<GoTermSummary> result = goTermSummaryService.searchByGoTerm(goTerm);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据tissueCellType获取GO术语摘要信息
     */
    @GetMapping("/tissuecelltype/{tissueCellType}")
    public ResponseEntity<List<GoTermSummary>> getByTissueCellType(@PathVariable String tissueCellType) {
        List<GoTermSummary> result = goTermSummaryService.getByTissueCellType(tissueCellType);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据cellLine获取GO术语摘要信息
     */
    @GetMapping("/cellline/{cellLine}")
    public ResponseEntity<List<GoTermSummary>> getByCellLine(@PathVariable String cellLine) {
        List<GoTermSummary> result = goTermSummaryService.getByCellLine(cellLine);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据disease获取GO术语摘要信息
     */
    @GetMapping("/disease/{disease}")
    public ResponseEntity<List<GoTermSummary>> getByDisease(@PathVariable String disease) {
        List<GoTermSummary> result = goTermSummaryService.getByDisease(disease);
        return ResponseEntity.ok(result);
    }

    /**
     * 分页搜索GO术语摘要信息
     */
    @GetMapping("/search")
    public ResponseEntity<IPage<GoTermSummary>> searchGoTermSummaries(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<GoTermSummary> pageObj = new Page<>(page, size);
        IPage<GoTermSummary> result = goTermSummaryService.searchGoTermSummaries(keyword, pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 搜索GO术语摘要信息（不分页）
     */
    @GetMapping("/search/all")
    public ResponseEntity<List<GoTermSummary>> searchGoTermSummariesAll(@RequestParam String keyword) {
        List<GoTermSummary> result = goTermSummaryService.searchGoTermSummaries(keyword);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有唯一的goTerm列表
     */
    @GetMapping("/all-goterms")
    public ResponseEntity<List<String>> getAllUniqueGoTerms() {
        List<String> result = goTermSummaryService.getAllUniqueGoTerms();
        return ResponseEntity.ok(result);
    }
} 