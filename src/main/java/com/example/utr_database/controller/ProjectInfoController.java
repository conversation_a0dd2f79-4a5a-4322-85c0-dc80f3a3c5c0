package com.example.utr_database.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.ProjectInfo;
import com.example.utr_database.service.ProjectInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/projects")
@Tag(name = "项目信息", description = "项目信息相关操作")
public class ProjectInfoController {

    @Autowired
    private ProjectInfoService projectInfoService;
    
    @GetMapping
    @Operation(
        summary = "获取所有项目",
        description = "分页获取所有项目信息，支持排序",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "获取成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = Page.class))
            )
        }
    )
    public ResponseEntity<Page<ProjectInfo>> getAllProjects(
        @Parameter(description = "当前页码，从1开始") @RequestParam(defaultValue = "1") long current,
        @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") long size,
        @Parameter(description = "排序字段") @RequestParam(required = false) String sortField,
        @Parameter(description = "排序方式, true为升序, false为降序") @RequestParam(required = false, defaultValue = "true") boolean isAsc
    ) {
        Page<ProjectInfo> results;
        if (sortField != null && !sortField.isEmpty()) {
            results = projectInfoService.listAllProjects(current, size, sortField, isAsc);
        } else {
            results = projectInfoService.listAllProjects(current, size);
        }
        return ResponseEntity.ok(results);
    }

    @GetMapping("/all")
    @Operation(
        summary = "获取所有项目数据",
        description = "一次性获取所有项目信息，不分页",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "获取成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<ProjectInfo>> getAllProjectsNoPaging() {
        List<ProjectInfo> results = projectInfoService.getAllProjects();
        return ResponseEntity.ok(results);
    }
    
    @GetMapping("/search")
    @Operation(
        summary = "搜索项目",
        description = "根据关键词在projectId、bioProjectId、geoAccession、strategy、tissueOrCellType、cellLine、disease、srrNumber、pmid、translatedTranscriptsNumber等字段中进行全文搜索，支持排序",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "搜索成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = Page.class))
            )
        }
    )
    public ResponseEntity<Page<ProjectInfo>> searchProjects(
        @Parameter(description = "搜索关键词") @RequestParam String keyword,
        @Parameter(description = "当前页码，从1开始") @RequestParam(defaultValue = "1") long current,
        @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") long size,
        @Parameter(description = "排序字段") @RequestParam(required = false) String sortField,
        @Parameter(description = "排序方式, true为升序, false为降序") @RequestParam(required = false, defaultValue = "true") boolean isAsc
    ) {
        Page<ProjectInfo> results;
        if (sortField != null && !sortField.isEmpty()) {
            results = projectInfoService.searchProjects(keyword, current, size, sortField, isAsc);
        } else {
            results = projectInfoService.searchProjects(keyword, current, size);
        }
        return ResponseEntity.ok(results);
    }

    @GetMapping("/search/all")
    @Operation(
        summary = "搜索所有项目数据",
        description = "根据关键词搜索所有项目信息，不分页",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "搜索成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<ProjectInfo>> searchAllProjects(
        @Parameter(description = "搜索关键词") @RequestParam String keyword
    ) {
        List<ProjectInfo> results = projectInfoService.searchAllProjects(keyword);
        return ResponseEntity.ok(results);
    }
} 