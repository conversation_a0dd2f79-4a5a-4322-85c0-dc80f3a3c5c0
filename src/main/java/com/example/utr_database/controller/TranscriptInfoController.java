package com.example.utr_database.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.TranscriptInfo;
import com.example.utr_database.service.TranscriptInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/transcriptinfo")
@CrossOrigin(origins = "*")
public class TranscriptInfoController {

    @Autowired
    private TranscriptInfoService transcriptInfoService;

    /**
     * 分页获取所有转录本信息
     */
    @GetMapping
    public ResponseEntity<IPage<TranscriptInfo>> getAllTranscriptInfo(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<TranscriptInfo> pageObj = new Page<>(page, size);
        IPage<TranscriptInfo> result = transcriptInfoService.getAllTranscriptInfo(pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有转录本信息（不分页）
     */
    @GetMapping("/all")
    public ResponseEntity<List<TranscriptInfo>> getAllTranscriptInfo() {
        List<TranscriptInfo> result = transcriptInfoService.getAllTranscriptInfo();
        return ResponseEntity.ok(result);
    }

    /**
     * 根据转录本ID获取转录本信息
     */
    @GetMapping("/id/{transcriptId}")
    public ResponseEntity<TranscriptInfo> getTranscriptInfoById(@PathVariable String transcriptId) {
        TranscriptInfo transcriptInfo = transcriptInfoService.getTranscriptInfoById(transcriptId);
        if (transcriptInfo != null) {
            return ResponseEntity.ok(transcriptInfo);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 根据基因ID获取转录本信息
     */
    @GetMapping("/geneid/{geneId}")
    public ResponseEntity<List<TranscriptInfo>> getTranscriptInfoByGeneId(@PathVariable String geneId) {
        List<TranscriptInfo> result = transcriptInfoService.getTranscriptInfoByGeneId(geneId);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据基因符号获取转录本信息
     */
    @GetMapping("/genesymbol/{geneSymbol}")
    public ResponseEntity<List<TranscriptInfo>> getTranscriptInfoByGeneSymbol(@PathVariable String geneSymbol) {
        List<TranscriptInfo> result = transcriptInfoService.getTranscriptInfoByGeneSymbol(geneSymbol);
        return ResponseEntity.ok(result);
    }

    /**
     * 分页搜索转录本信息
     */
    @GetMapping("/search")
    public ResponseEntity<IPage<TranscriptInfo>> searchTranscriptInfo(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<TranscriptInfo> pageObj = new Page<>(page, size);
        IPage<TranscriptInfo> result = transcriptInfoService.searchTranscriptInfo(keyword, pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 搜索转录本信息（不分页）
     */
    @GetMapping("/search/all")
    public ResponseEntity<List<TranscriptInfo>> searchTranscriptInfoAll(@RequestParam String keyword) {
        List<TranscriptInfo> result = transcriptInfoService.searchTranscriptInfo(keyword);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取前50个唯一的transcriptId（按升序排列）
     */
    @GetMapping("/top50-transcript-ids")
    public ResponseEntity<List<String>> getTop50UniqueTranscriptIds() {
        List<String> result = transcriptInfoService.getTop50UniqueTranscriptIds();
        return ResponseEntity.ok(result);
    }
} 