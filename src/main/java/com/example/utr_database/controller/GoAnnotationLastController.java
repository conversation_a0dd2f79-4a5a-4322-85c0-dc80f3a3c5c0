package com.example.utr_database.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GoAnnotationLast;
import com.example.utr_database.service.GoAnnotationLastService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/goannotationlast")
@CrossOrigin(origins = "*")
public class GoAnnotationLastController {

    @Autowired
    private GoAnnotationLastService goAnnotationLastService;

    /**
     * 分页获取所有GO注释信息
     */
    @GetMapping
    public ResponseEntity<IPage<GoAnnotationLast>> getAllGoAnnotationLasts(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<GoAnnotationLast> pageObj = new Page<>(page, size);
        IPage<GoAnnotationLast> result = goAnnotationLastService.getAllGoAnnotationLasts(pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有GO注释信息（不分页）
     */
    @GetMapping("/all")
    public ResponseEntity<List<GoAnnotationLast>> getAllGoAnnotationLasts() {
        List<GoAnnotationLast> result = goAnnotationLastService.getAllGoAnnotationLasts();
        return ResponseEntity.ok(result);
    }

    /**
     * 根据ID获取GO注释信息
     */
    @GetMapping("/id/{id}")
    public ResponseEntity<GoAnnotationLast> getGoAnnotationLastById(@PathVariable Integer id) {
        GoAnnotationLast goAnnotationLast = goAnnotationLastService.getGoAnnotationLastById(id);
        if (goAnnotationLast != null) {
            return ResponseEntity.ok(goAnnotationLast);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 根据goTerm获取所有相关的GO注释信息（核心功能）
     */
    @GetMapping("/goterm/{goTerm}")
    public ResponseEntity<List<GoAnnotationLast>> getByGoTerm(@PathVariable String goTerm) {
        List<GoAnnotationLast> result = goAnnotationLastService.getByGoTerm(goTerm);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据geneSymbol获取GO注释信息
     */
    @GetMapping("/genesymbol/{geneSymbol}")
    public ResponseEntity<List<GoAnnotationLast>> getByGeneSymbol(@PathVariable String geneSymbol) {
        List<GoAnnotationLast> result = goAnnotationLastService.getByGeneSymbol(geneSymbol);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据geneId获取GO注释信息
     */
    @GetMapping("/geneid/{geneId}")
    public ResponseEntity<List<GoAnnotationLast>> getByGeneId(@PathVariable String geneId) {
        List<GoAnnotationLast> result = goAnnotationLastService.getByGeneId(geneId);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据goId获取GO注释信息
     */
    @GetMapping("/goid/{goId}")
    public ResponseEntity<List<GoAnnotationLast>> getByGoId(@PathVariable String goId) {
        List<GoAnnotationLast> result = goAnnotationLastService.getByGoId(goId);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据goDomain获取GO注释信息
     */
    @GetMapping("/godomain/{goDomain}")
    public ResponseEntity<List<GoAnnotationLast>> getByGoDomain(@PathVariable String goDomain) {
        List<GoAnnotationLast> result = goAnnotationLastService.getByGoDomain(goDomain);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据translationProjectIds获取GO注释信息
     */
    @GetMapping("/projectids/{translationProjectIds}")
    public ResponseEntity<List<GoAnnotationLast>> getByTranslationProjectIds(@PathVariable String translationProjectIds) {
        List<GoAnnotationLast> result = goAnnotationLastService.getByTranslationProjectIds(translationProjectIds);
        return ResponseEntity.ok(result);
    }

    /**
     * 分页搜索GO注释信息
     */
    @GetMapping("/search")
    public ResponseEntity<IPage<GoAnnotationLast>> searchGoAnnotationLasts(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<GoAnnotationLast> pageObj = new Page<>(page, size);
        IPage<GoAnnotationLast> result = goAnnotationLastService.searchGoAnnotationLasts(keyword, pageObj);
        return ResponseEntity.ok(result);
    }

    /**
     * 搜索GO注释信息（不分页）
     */
    @GetMapping("/search/all")
    public ResponseEntity<List<GoAnnotationLast>> searchGoAnnotationLastsAll(@RequestParam String keyword) {
        List<GoAnnotationLast> result = goAnnotationLastService.searchGoAnnotationLasts(keyword);
        return ResponseEntity.ok(result);
    }
}
