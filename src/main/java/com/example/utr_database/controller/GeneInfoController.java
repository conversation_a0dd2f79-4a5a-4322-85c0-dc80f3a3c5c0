package com.example.utr_database.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.GeneInfo;
import com.example.utr_database.service.GeneInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/gene-info")
@Tag(name = "基因信息", description = "基因详细信息相关操作")
public class GeneInfoController {

    @Autowired
    private GeneInfoService geneInfoService;
    
    @GetMapping
    @Operation(
        summary = "获取所有基因信息",
        description = "分页获取所有基因详细信息，支持排序",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "获取成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = Page.class))
            )
        }
    )
    public ResponseEntity<Page<GeneInfo>> getAllGeneInfo(
        @Parameter(description = "当前页码，从1开始") @RequestParam(defaultValue = "1") long current,
        @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") long size,
        @Parameter(description = "排序字段") @RequestParam(required = false) String sortField,
        @Parameter(description = "排序方式, true为升序, false为降序") @RequestParam(required = false, defaultValue = "true") boolean isAsc
    ) {
        Page<GeneInfo> results;
        if (sortField != null && !sortField.isEmpty()) {
            results = geneInfoService.listAllGeneInfo(current, size, sortField, isAsc);
        } else {
            results = geneInfoService.listAllGeneInfo(current, size);
        }
        return ResponseEntity.ok(results);
    }

    @GetMapping("/all")
    @Operation(
        summary = "获取所有基因信息数据",
        description = "一次性获取所有基因详细信息，不分页",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "获取成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<GeneInfo>> getAllGeneInfoNoPaging() {
        List<GeneInfo> results = geneInfoService.getAllGeneInfo();
        return ResponseEntity.ok(results);
    }
    
    @GetMapping("/search")
    @Operation(
        summary = "搜索基因信息",
        description = "根据关键词在geneId、geneSymbol、approvedName、locusType、chromosome、transcriptCount等字段中进行全文搜索，支持排序",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "搜索成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = Page.class))
            )
        }
    )
    public ResponseEntity<Page<GeneInfo>> searchGeneInfo(
        @Parameter(description = "搜索关键词") @RequestParam String keyword,
        @Parameter(description = "当前页码，从1开始") @RequestParam(defaultValue = "1") long current,
        @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") long size,
        @Parameter(description = "排序字段") @RequestParam(required = false) String sortField,
        @Parameter(description = "排序方式, true为升序, false为降序") @RequestParam(required = false, defaultValue = "true") boolean isAsc
    ) {
        Page<GeneInfo> results;
        if (sortField != null && !sortField.isEmpty()) {
            results = geneInfoService.searchGeneInfo(keyword, current, size, sortField, isAsc);
        } else {
            results = geneInfoService.searchGeneInfo(keyword, current, size);
        }
        return ResponseEntity.ok(results);
    }

    @GetMapping("/search/all")
    @Operation(
        summary = "搜索所有基因信息数据",
        description = "根据关键词搜索所有基因详细信息，不分页",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "搜索成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<GeneInfo>> searchAllGeneInfo(
        @Parameter(description = "搜索关键词") @RequestParam String keyword
    ) {
        List<GeneInfo> results = geneInfoService.searchAllGeneInfo(keyword);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/id/{geneId}")
    @Operation(
        summary = "根据geneId查询基因信息",
        description = "根据指定的geneId精确查询基因详细信息",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = GeneInfo.class))
            )
        }
    )
    public ResponseEntity<GeneInfo> getByGeneId(
        @Parameter(description = "基因ID") @PathVariable String geneId
    ) {
        GeneInfo result = geneInfoService.getByGeneId(geneId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/symbol/{geneSymbol}")
    @Operation(
        summary = "根据geneSymbol查询基因信息",
        description = "根据指定的基因符号查询基因详细信息",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<GeneInfo>> getByGeneSymbol(
        @Parameter(description = "基因符号") @PathVariable String geneSymbol
    ) {
        List<GeneInfo> results = geneInfoService.getByGeneSymbol(geneSymbol);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/locus-type/{locusType}")
    @Operation(
        summary = "根据locusType查询基因信息",
        description = "根据指定的位点类型查询基因详细信息",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<GeneInfo>> getByLocusType(
        @Parameter(description = "位点类型") @PathVariable String locusType
    ) {
        List<GeneInfo> results = geneInfoService.getByLocusType(locusType);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/transcript-count")
    @Operation(
        summary = "根据转录本数量范围查询基因信息",
        description = "根据指定的转录本数量范围查询基因详细信息",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<GeneInfo>> getByTranscriptCountRange(
        @Parameter(description = "最小转录本数量") @RequestParam(required = false) Integer minCount,
        @Parameter(description = "最大转录本数量") @RequestParam(required = false) Integer maxCount
    ) {
        List<GeneInfo> results = geneInfoService.getByTranscriptCountRange(minCount, maxCount);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/gene-id-symbol")
    @Operation(
        summary = "根据geneId和geneSymbol复合条件查询基因信息",
        description = "根据指定的geneId和geneSymbol组合查询基因详细信息",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<GeneInfo>> getByGeneIdAndGeneSymbol(
        @Parameter(description = "基因ID") @RequestParam String geneId,
        @Parameter(description = "基因符号") @RequestParam String geneSymbol
    ) {
        List<GeneInfo> results = geneInfoService.getByGeneIdAndGeneSymbol(geneId, geneSymbol);
        return ResponseEntity.ok(results);
    }
    
    @GetMapping("/all-gene-ids")
    @Operation(
        summary = "获取所有唯一的geneId",
        description = "返回数据库中所有唯一的geneId列表，按升序排列，不包含其他信息",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "获取成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<String>> getAllUniqueGeneIds() {
        List<String> results = geneInfoService.getTop50UniqueGeneIds();
        return ResponseEntity.ok(results);
    }

    @GetMapping("/all-gene-symbols")
    @Operation(
        summary = "获取所有唯一的geneSymbol",
        description = "返回数据库中所有唯一的geneSymbol列表，按字母顺序排列，不包含其他信息",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "获取成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<String>> getAllUniqueGeneSymbols() {
        List<String> results = geneInfoService.getTop50UniqueGeneSymbols();
        return ResponseEntity.ok(results);
    }
} 