package com.example.utr_database.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.utr_database.entity.Gene;
import com.example.utr_database.service.GeneService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/genes")
@Tag(name = "基因信息", description = "基因信息相关操作")
public class GeneController {

    @Autowired
    private GeneService geneService;
    
    @GetMapping
    @Operation(
        summary = "获取所有基因",
        description = "分页获取所有基因信息，支持排序",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "获取成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = Page.class))
            )
        }
    )
    public ResponseEntity<Page<Gene>> getAllGenes(
        @Parameter(description = "当前页码，从1开始") @RequestParam(defaultValue = "1") long current,
        @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") long size,
        @Parameter(description = "排序字段") @RequestParam(required = false) String sortField,
        @Parameter(description = "排序方式, true为升序, false为降序") @RequestParam(required = false, defaultValue = "true") boolean isAsc
    ) {
        Page<Gene> results;
        if (sortField != null && !sortField.isEmpty()) {
            results = geneService.listAllGenes(current, size, sortField, isAsc);
        } else {
            results = geneService.listAllGenes(current, size);
        }
        return ResponseEntity.ok(results);
    }

    @GetMapping("/all")
    @Operation(
        summary = "获取所有基因数据",
        description = "一次性获取所有基因信息，不分页",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "获取成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<Gene>> getAllGenesNoPaging() {
        List<Gene> results = geneService.getAllGenes();
        return ResponseEntity.ok(results);
    }
    
    @GetMapping("/search")
    @Operation(
        summary = "搜索基因",
        description = "根据关键词在geneSymbol、geneId、projectId、expressedTranscriptNumber、tissueCellType、cellLine、disease等字段中进行全文搜索，支持排序",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "搜索成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = Page.class))
            )
        }
    )
    public ResponseEntity<Page<Gene>> searchGenes(
        @Parameter(description = "搜索关键词") @RequestParam String keyword,
        @Parameter(description = "当前页码，从1开始") @RequestParam(defaultValue = "1") long current,
        @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") long size,
        @Parameter(description = "排序字段") @RequestParam(required = false) String sortField,
        @Parameter(description = "排序方式, true为升序, false为降序") @RequestParam(required = false, defaultValue = "true") boolean isAsc
    ) {
        Page<Gene> results;
        if (sortField != null && !sortField.isEmpty()) {
            results = geneService.searchGenes(keyword, current, size, sortField, isAsc);
        } else {
            results = geneService.searchGenes(keyword, current, size);
        }
        return ResponseEntity.ok(results);
    }

    @GetMapping("/search/all")
    @Operation(
        summary = "搜索所有基因数据",
        description = "根据关键词搜索所有基因信息，不分页",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "搜索成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<Gene>> searchAllGenes(
        @Parameter(description = "搜索关键词") @RequestParam String keyword
    ) {
        List<Gene> results = geneService.searchAllGenes(keyword);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/geneId/{geneId}")
    @Operation(
        summary = "根据geneId查询基因",
        description = "根据指定的geneId精确查询基因信息",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<Gene>> getGenesByGeneId(
        @Parameter(description = "基因ID") @org.springframework.web.bind.annotation.PathVariable String geneId
    ) {
        List<Gene> results = geneService.getGenesByGeneId(geneId);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/projectId/{projectId}")
    @Operation(
        summary = "根据projectId查询基因",
        description = "根据指定的projectId查询该项目下的所有基因信息",
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<Gene>> getGenesByProjectId(
        @Parameter(description = "项目ID") @org.springframework.web.bind.annotation.PathVariable String projectId
    ) {
        List<Gene> results = geneService.getGenesByProjectId(projectId);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/geneSymbol/{geneSymbol}")
    @Operation(
        summary = "根据geneSymbol查询基因",
        description = "根据指定的geneSymbol查询基因信息",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<Gene>> getGenesByGeneSymbol(
        @Parameter(description = "基因符号") @org.springframework.web.bind.annotation.PathVariable String geneSymbol
    ) {
        List<Gene> results = geneService.getGenesByGeneSymbol(geneSymbol);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/filter")
    @Operation(
        summary = "根据多个条件进行复合查询基因",
        description = "根据多个geneId（必需）和可选的tissueCellType、cellLine、disease、chromosome条件筛选基因信息。geneIds可以传入多个值，用逗号分隔。可选条件如果不需要筛选，可以省略参数、传递空值或传递'None'",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class))
            )
        }
    )
    public ResponseEntity<List<Gene>> getByMultipleConditions(
        @Parameter(description = "基因ID列表，多个ID用逗号分隔", required = true) @RequestParam String geneIds,
        @Parameter(description = "组织细胞类型（可选，传递'None'表示不筛选此条件）") @RequestParam(required = false) String tissueCellType,
        @Parameter(description = "细胞系（可选，传递'None'表示不筛选此条件）") @RequestParam(required = false) String cellLine,
        @Parameter(description = "疾病（可选，传递'None'表示不筛选此条件）") @RequestParam(required = false) String disease,
        @Parameter(description = "染色体（可选，传递'None'表示不筛选此条件）") @RequestParam(required = false) String chromosome
    ) {
        // 将逗号分隔的geneIds字符串转换为List
        List<String> geneIdList = java.util.Arrays.asList(geneIds.split(","))
                .stream()
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .collect(java.util.stream.Collectors.toList());

        List<Gene> results = geneService.getByMultipleConditions(
            geneIdList, tissueCellType, cellLine, disease, chromosome
        );
        return ResponseEntity.ok(results);
    }
}