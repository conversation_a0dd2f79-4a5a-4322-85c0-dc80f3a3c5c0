package com.example.utr_database.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * 缓存配置类
 * 使用高性能的Caffeine缓存实现
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * 配置高性能的Caffeine缓存管理器
     */
    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        // 设置默认的缓存规则
        cacheManager.setCaffeine(Caffeine.newBuilder()
                // 设置缓存的初始容量
                .initialCapacity(100)
                // 设置缓存的最大容量
                .maximumSize(500)
                // 设置缓存过期时间：写入后10分钟过期
                .expireAfterWrite(10, TimeUnit.MINUTES)
                // 设置缓存过期时间：最后一次访问后5分钟过期
                .expireAfterAccess(5, TimeUnit.MINUTES)
                // 开启缓存统计
                .recordStats());
        
        // 设置所有的缓存名称
        cacheManager.setCacheNames(Arrays.asList(
                "transcriptsAccessions",
                "utr5IDs", 
                "utr3IDs", 
                "geneSymbols",
                "allTranscriptions",
                "allTranscriptionsSorted"
        ));
        
        return cacheManager;
    }
} 