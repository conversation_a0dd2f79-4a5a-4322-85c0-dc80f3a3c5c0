package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("goAnnotation")
public class GoAnnotation {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("geneSymbol")
    private String geneSymbol;

    @TableField("geneId")
    private String geneId;

    @TableField("goTerm")
    private String goTerm;

    @TableField("goDomain")
    private String goDomain;

    @TableField("goId")
    private String goId;

    @TableField("tissueCellType")
    private String tissueCellType;

    @TableField("cellLine")
    private String cellLine;

    @TableField("disease")
    private String disease;

    // 构造函数
    public GoAnnotation() {}

    // Getter和Setter方法
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGeneSymbol() {
        return geneSymbol;
    }

    public void setGeneSymbol(String geneSymbol) {
        this.geneSymbol = geneSymbol;
    }

    public String getGeneId() {
        return geneId;
    }

    public void setGeneId(String geneId) {
        this.geneId = geneId;
    }

    public String getGoTerm() {
        return goTerm;
    }

    public void setGoTerm(String goTerm) {
        this.goTerm = goTerm;
    }

    public String getGoDomain() {
        return goDomain;
    }

    public void setGoDomain(String goDomain) {
        this.goDomain = goDomain;
    }

    public String getGoId() {
        return goId;
    }

    public void setGoId(String goId) {
        this.goId = goId;
    }

    public String getTissueCellType() {
        return tissueCellType;
    }

    public void setTissueCellType(String tissueCellType) {
        this.tissueCellType = tissueCellType;
    }

    public String getCellLine() {
        return cellLine;
    }

    public void setCellLine(String cellLine) {
        this.cellLine = cellLine;
    }

    public String getDisease() {
        return disease;
    }

    public void setDisease(String disease) {
        this.disease = disease;
    }

    @Override
    public String toString() {
        return "GoAnnotation{" +
                "id=" + id +
                ", geneSymbol='" + geneSymbol + '\'' +
                ", geneId='" + geneId + '\'' +
                ", goTerm='" + goTerm + '\'' +
                ", goDomain='" + goDomain + '\'' +
                ", goId='" + goId + '\'' +
                ", tissueCellType='" + tissueCellType + '\'' +
                ", cellLine='" + cellLine + '\'' +
                ", disease='" + disease + '\'' +
                '}';
    }
} 