package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("gene")
public class Gene {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    @TableField("geneSymbol")
    private String geneSymbol;
    
    @TableField("geneId")
    private String geneId;
    
    @TableField("projectId")
    private String projectId;
    
    @TableField("expressedTranscriptNumber")
    private Integer expressedTranscriptNumber;
    
    @TableField("tissueCellType")
    private String tissueCellType;
    
    @TableField("cellLine")
    private String cellLine;
    
    @TableField("disease")
    private String disease;
    
    @TableField("chromosome")
    private String chromosome;

    @TableField("TR")
    private BigDecimal tr;

    @TableField("EVI")
    private BigDecimal evi;

    @TableField("TE")
    private BigDecimal te;

    // 手动添加所有getter和setter方法以确保JSON序列化正常
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getGeneSymbol() {
        return geneSymbol;
    }
    
    public void setGeneSymbol(String geneSymbol) {
        this.geneSymbol = geneSymbol;
    }
    
    public String getGeneId() {
        return geneId;
    }
    
    public void setGeneId(String geneId) {
        this.geneId = geneId;
    }
    
    public String getProjectId() {
        return projectId;
    }
    
    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
    
    public Integer getExpressedTranscriptNumber() {
        return expressedTranscriptNumber;
    }
    
    public void setExpressedTranscriptNumber(Integer expressedTranscriptNumber) {
        this.expressedTranscriptNumber = expressedTranscriptNumber;
    }
    
    public String getTissueCellType() {
        return tissueCellType;
    }
    
    public void setTissueCellType(String tissueCellType) {
        this.tissueCellType = tissueCellType;
    }
    
    public String getCellLine() {
        return cellLine;
    }
    
    public void setCellLine(String cellLine) {
        this.cellLine = cellLine;
    }
    
    public String getDisease() {
        return disease;
    }
    
    public void setDisease(String disease) {
        this.disease = disease;
    }
    
    public String getChromosome() { return chromosome; }
    public void setChromosome(String chromosome) { this.chromosome = chromosome; }

    public BigDecimal getTr() {
        return tr;
    }

    public void setTr(BigDecimal tr) {
        this.tr = tr;
    }

    public BigDecimal getEvi() {
        return evi;
    }

    public void setEvi(BigDecimal evi) {
        this.evi = evi;
    }

    public BigDecimal getTe() {
        return te;
    }

    public void setTe(BigDecimal te) {
        this.te = te;
    }
}