package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;

@TableName("transcriptInfo")
public class TranscriptInfo {
    @TableId("transcriptId")
    private String transcriptId;

    @TableField("geneId")
    private String geneId;

    @TableField("geneSymbol")
    private String geneSymbol;

    @TableField("threeUtrEntryName")
    private String threeUtrEntryName;

    @TableField("threeUtrLength")
    private BigDecimal threeUtrLength;

    @TableField("threeUtrUrl")
    private String threeUtrUrl;

    @TableField("threeUtrMiRnas")
    private String threeUtrMiRnas;

    @TableField("threeUtrPolyaSites")
    private String threeUtrPolyaSites;

    @TableField("threeUtrRfamMotifs")
    private String threeUtrRfamMotifs;

    @TableField("threeUtrRepeats")
    private String threeUtrRepeats;

    @TableField("threeUtrUorfs")
    private String threeUtrUorfs;

    @TableField("threeUtrIres")
    private String threeUtrIres;

    @TableField("fiveUtrEntryName")
    private String fiveUtrEntryName;

    @TableField("fiveUtrLength")
    private BigDecimal fiveUtrLength;

    @TableField("fiveUtrUrl")
    private String fiveUtrUrl;

    @TableField("fiveUtrMiRnas")
    private String fiveUtrMiRnas;

    @TableField("fiveUtrPolyaSites")
    private String fiveUtrPolyaSites;

    @TableField("fiveUtrRfamMotifs")
    private String fiveUtrRfamMotifs;

    @TableField("fiveUtrRepeats")
    private String fiveUtrRepeats;

    @TableField("fiveUtrUorfs")
    private String fiveUtrUorfs;

    @TableField("fiveUtrIres")
    private String fiveUtrIres;

    @TableField("transcriptLocation")
    private String transcriptLocation;

    // 构造函数
    public TranscriptInfo() {}

    // Getter和Setter方法
    public String getTranscriptId() {
        return transcriptId;
    }

    public void setTranscriptId(String transcriptId) {
        this.transcriptId = transcriptId;
    }

    public String getGeneId() {
        return geneId;
    }

    public void setGeneId(String geneId) {
        this.geneId = geneId;
    }

    public String getGeneSymbol() {
        return geneSymbol;
    }

    public void setGeneSymbol(String geneSymbol) {
        this.geneSymbol = geneSymbol;
    }

    public String getThreeUtrEntryName() {
        return threeUtrEntryName;
    }

    public void setThreeUtrEntryName(String threeUtrEntryName) {
        this.threeUtrEntryName = threeUtrEntryName;
    }

    public BigDecimal getThreeUtrLength() {
        return threeUtrLength;
    }

    public void setThreeUtrLength(BigDecimal threeUtrLength) {
        this.threeUtrLength = threeUtrLength;
    }

    public String getThreeUtrUrl() {
        return threeUtrUrl;
    }

    public void setThreeUtrUrl(String threeUtrUrl) {
        this.threeUtrUrl = threeUtrUrl;
    }

    public String getThreeUtrMiRnas() {
        return threeUtrMiRnas;
    }

    public void setThreeUtrMiRnas(String threeUtrMiRnas) {
        this.threeUtrMiRnas = threeUtrMiRnas;
    }

    public String getThreeUtrPolyaSites() {
        return threeUtrPolyaSites;
    }

    public void setThreeUtrPolyaSites(String threeUtrPolyaSites) {
        this.threeUtrPolyaSites = threeUtrPolyaSites;
    }

    public String getThreeUtrRfamMotifs() {
        return threeUtrRfamMotifs;
    }

    public void setThreeUtrRfamMotifs(String threeUtrRfamMotifs) {
        this.threeUtrRfamMotifs = threeUtrRfamMotifs;
    }

    public String getThreeUtrRepeats() {
        return threeUtrRepeats;
    }

    public void setThreeUtrRepeats(String threeUtrRepeats) {
        this.threeUtrRepeats = threeUtrRepeats;
    }

    public String getThreeUtrUorfs() {
        return threeUtrUorfs;
    }

    public void setThreeUtrUorfs(String threeUtrUorfs) {
        this.threeUtrUorfs = threeUtrUorfs;
    }

    public String getFiveUtrEntryName() {
        return fiveUtrEntryName;
    }

    public void setFiveUtrEntryName(String fiveUtrEntryName) {
        this.fiveUtrEntryName = fiveUtrEntryName;
    }

    public BigDecimal getFiveUtrLength() {
        return fiveUtrLength;
    }

    public void setFiveUtrLength(BigDecimal fiveUtrLength) {
        this.fiveUtrLength = fiveUtrLength;
    }

    public String getFiveUtrUrl() {
        return fiveUtrUrl;
    }

    public void setFiveUtrUrl(String fiveUtrUrl) {
        this.fiveUtrUrl = fiveUtrUrl;
    }

    public String getFiveUtrMiRnas() {
        return fiveUtrMiRnas;
    }

    public void setFiveUtrMiRnas(String fiveUtrMiRnas) {
        this.fiveUtrMiRnas = fiveUtrMiRnas;
    }

    public String getFiveUtrPolyaSites() {
        return fiveUtrPolyaSites;
    }

    public void setFiveUtrPolyaSites(String fiveUtrPolyaSites) {
        this.fiveUtrPolyaSites = fiveUtrPolyaSites;
    }

    public String getFiveUtrRfamMotifs() {
        return fiveUtrRfamMotifs;
    }

    public void setFiveUtrRfamMotifs(String fiveUtrRfamMotifs) {
        this.fiveUtrRfamMotifs = fiveUtrRfamMotifs;
    }

    public String getFiveUtrRepeats() {
        return fiveUtrRepeats;
    }

    public void setFiveUtrRepeats(String fiveUtrRepeats) {
        this.fiveUtrRepeats = fiveUtrRepeats;
    }

    public String getFiveUtrUorfs() {
        return fiveUtrUorfs;
    }

    public void setFiveUtrUorfs(String fiveUtrUorfs) {
        this.fiveUtrUorfs = fiveUtrUorfs;
    }

    public String getThreeUtrIres() {
        return threeUtrIres;
    }

    public void setThreeUtrIres(String threeUtrIres) {
        this.threeUtrIres = threeUtrIres;
    }

    public String getFiveUtrIres() {
        return fiveUtrIres;
    }

    public void setFiveUtrIres(String fiveUtrIres) {
        this.fiveUtrIres = fiveUtrIres;
    }

    public String getTranscriptLocation() {
        return transcriptLocation;
    }

    public void setTranscriptLocation(String transcriptLocation) {
        this.transcriptLocation = transcriptLocation;
    }

    @Override
    public String toString() {
        return "TranscriptInfo{" +
                "transcriptId='" + transcriptId + '\'' +
                ", geneId='" + geneId + '\'' +
                ", geneSymbol='" + geneSymbol + '\'' +
                ", threeUtrEntryName='" + threeUtrEntryName + '\'' +
                ", threeUtrLength=" + threeUtrLength +
                ", threeUtrUrl='" + threeUtrUrl + '\'' +
                ", threeUtrMiRnas='" + threeUtrMiRnas + '\'' +
                ", threeUtrPolyaSites='" + threeUtrPolyaSites + '\'' +
                ", threeUtrRfamMotifs='" + threeUtrRfamMotifs + '\'' +
                ", threeUtrRepeats='" + threeUtrRepeats + '\'' +
                ", threeUtrUorfs='" + threeUtrUorfs + '\'' +
                ", threeUtrIres='" + threeUtrIres + '\'' +
                ", fiveUtrEntryName='" + fiveUtrEntryName + '\'' +
                ", fiveUtrLength=" + fiveUtrLength +
                ", fiveUtrUrl='" + fiveUtrUrl + '\'' +
                ", fiveUtrMiRnas='" + fiveUtrMiRnas + '\'' +
                ", fiveUtrPolyaSites='" + fiveUtrPolyaSites + '\'' +
                ", fiveUtrRfamMotifs='" + fiveUtrRfamMotifs + '\'' +
                ", fiveUtrRepeats='" + fiveUtrRepeats + '\'' +
                ", fiveUtrUorfs='" + fiveUtrUorfs + '\'' +
                ", fiveUtrIres='" + fiveUtrIres + '\'' +
                ", transcriptLocation='" + transcriptLocation + '\'' +
                '}';
    }
} 