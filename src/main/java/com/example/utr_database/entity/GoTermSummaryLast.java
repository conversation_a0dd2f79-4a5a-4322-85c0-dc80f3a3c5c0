package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("goTermSummaryLast")
public class GoTermSummaryLast {
    @TableId("goTerm")
    private String goTerm;

    @TableField("translationProjectIds")
    private String translationProjectIds;

    // 构造函数
    public GoTermSummaryLast() {}

    public GoTermSummaryLast(String goTerm, String translationProjectIds) {
        this.goTerm = goTerm;
        this.translationProjectIds = translationProjectIds;
    }

    // Getter和Setter方法
    public String getGoTerm() {
        return goTerm;
    }

    public void setGoTerm(String goTerm) {
        this.goTerm = goTerm;
    }

    public String getTranslationProjectIds() {
        return translationProjectIds;
    }

    public void setTranslationProjectIds(String translationProjectIds) {
        this.translationProjectIds = translationProjectIds;
    }

    @Override
    public String toString() {
        return "GoTermSummaryLast{" +
                "goTerm='" + goTerm + '\'' +
                ", translationProjectIds='" + translationProjectIds + '\'' +
                '}';
    }
}
