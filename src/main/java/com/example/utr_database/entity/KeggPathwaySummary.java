package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("keggPathwaySummary")
public class KeggPathwaySummary {
    @TableId("pathwayDescription")
    private String pathwayDescription;

    @TableField("tissueCellType")
    private String tissueCellType;

    @TableField("cellLine")
    private String cellLine;

    @TableField("disease")
    private String disease;

    // 构造函数
    public KeggPathwaySummary() {}

    public KeggPathwaySummary(String pathwayDescription, String tissueCellType, String cellLine, String disease) {
        this.pathwayDescription = pathwayDescription;
        this.tissueCellType = tissueCellType;
        this.cellLine = cellLine;
        this.disease = disease;
    }

    // Getter和Setter方法
    public String getPathwayDescription() {
        return pathwayDescription;
    }

    public void setPathwayDescription(String pathwayDescription) {
        this.pathwayDescription = pathwayDescription;
    }

    public String getTissueCellType() {
        return tissueCellType;
    }

    public void setTissueCellType(String tissueCellType) {
        this.tissueCellType = tissueCellType;
    }

    public String getCellLine() {
        return cellLine;
    }

    public void setCellLine(String cellLine) {
        this.cellLine = cellLine;
    }

    public String getDisease() {
        return disease;
    }

    public void setDisease(String disease) {
        this.disease = disease;
    }

    @Override
    public String toString() {
        return "KeggPathwaySummary{" +
                "pathwayDescription='" + pathwayDescription + '\'' +
                ", tissueCellType='" + tissueCellType + '\'' +
                ", cellLine='" + cellLine + '\'' +
                ", disease='" + disease + '\'' +
                '}';
    }
} 