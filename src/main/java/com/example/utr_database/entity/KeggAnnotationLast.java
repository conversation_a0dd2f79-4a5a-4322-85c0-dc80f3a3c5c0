package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("keggAnnotationLast")
public class KeggAnnotationLast {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("geneSymbol")
    private String geneSymbol;

    @TableField("geneId")
    private String geneId;

    @TableField("pathwayDescription")
    private String pathwayDescription;

    @TableField("pathwayId")
    private String pathwayId;

    @TableField("translationProjectIds")
    private String translationProjectIds;

    // 构造函数
    public KeggAnnotationLast() {}

    // Getter和Setter方法
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGeneSymbol() {
        return geneSymbol;
    }

    public void setGeneSymbol(String geneSymbol) {
        this.geneSymbol = geneSymbol;
    }

    public String getGeneId() {
        return geneId;
    }

    public void setGeneId(String geneId) {
        this.geneId = geneId;
    }

    public String getPathwayDescription() {
        return pathwayDescription;
    }

    public void setPathwayDescription(String pathwayDescription) {
        this.pathwayDescription = pathwayDescription;
    }

    public String getPathwayId() {
        return pathwayId;
    }

    public void setPathwayId(String pathwayId) {
        this.pathwayId = pathwayId;
    }

    public String getTranslationProjectIds() {
        return translationProjectIds;
    }

    public void setTranslationProjectIds(String translationProjectIds) {
        this.translationProjectIds = translationProjectIds;
    }

    @Override
    public String toString() {
        return "KeggAnnotationLast{" +
                "id=" + id +
                ", geneSymbol='" + geneSymbol + '\'' +
                ", geneId='" + geneId + '\'' +
                ", pathwayDescription='" + pathwayDescription + '\'' +
                ", pathwayId='" + pathwayId + '\'' +
                ", translationProjectIds='" + translationProjectIds + '\'' +
                '}';
    }
}
