package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("keggPathwaySummaryLast")
public class KeggPathwaySummaryLast {
    @TableId("pathwayDescription")
    private String pathwayDescription;

    @TableField("translationProjectIds")
    private String translationProjectIds;

    // 构造函数
    public KeggPathwaySummaryLast() {}

    public KeggPathwaySummaryLast(String pathwayDescription, String translationProjectIds) {
        this.pathwayDescription = pathwayDescription;
        this.translationProjectIds = translationProjectIds;
    }

    // Getter和Setter方法
    public String getPathwayDescription() {
        return pathwayDescription;
    }

    public void setPathwayDescription(String pathwayDescription) {
        this.pathwayDescription = pathwayDescription;
    }

    public String getTranslationProjectIds() {
        return translationProjectIds;
    }

    public void setTranslationProjectIds(String translationProjectIds) {
        this.translationProjectIds = translationProjectIds;
    }

    @Override
    public String toString() {
        return "KeggPathwaySummaryLast{" +
                "pathwayDescription='" + pathwayDescription + '\'' +
                ", translationProjectIds='" + translationProjectIds + '\'' +
                '}';
    }
}
