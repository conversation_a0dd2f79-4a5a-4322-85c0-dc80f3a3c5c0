package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("geneInfo")
public class GeneInfo {
    
    @TableId("geneId")
    private String geneId;
    
    @TableField("geneSymbol")
    private String geneSymbol;
    
    @TableField("approvedName")
    private String approvedName;
    
    @TableField("locusType")
    private String locusType;
    
    @TableField("chromosome")
    private String chromosome;
    
    @TableField("transcriptCount")
    private Integer transcriptCount;
    
    @TableField("transcripts")
    private String transcripts;
    
    // 手动添加所有getter和setter方法以确保JSON序列化正常
    public String getGeneId() {
        return geneId;
    }
    
    public void setGeneId(String geneId) {
        this.geneId = geneId;
    }
    
    public String getGeneSymbol() {
        return geneSymbol;
    }
    
    public void setGeneSymbol(String geneSymbol) {
        this.geneSymbol = geneSymbol;
    }
    
    public String getApprovedName() {
        return approvedName;
    }
    
    public void setApprovedName(String approvedName) {
        this.approvedName = approvedName;
    }
    
    public String getLocusType() {
        return locusType;
    }
    
    public void setLocusType(String locusType) {
        this.locusType = locusType;
    }
    
    public String getChromosome() {
        return chromosome;
    }
    
    public void setChromosome(String chromosome) {
        this.chromosome = chromosome;
    }
    
    public Integer getTranscriptCount() {
        return transcriptCount;
    }
    
    public void setTranscriptCount(Integer transcriptCount) {
        this.transcriptCount = transcriptCount;
    }
    
    public String getTranscripts() {
        return transcripts;
    }
    
    public void setTranscripts(String transcripts) {
        this.transcripts = transcripts;
    }
} 