package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("goTermSummary")
public class GoTermSummary {
    @TableId("goTerm")
    private String goTerm;

    @TableField("tissueCellType")
    private String tissueCellType;

    @TableField("cellLine")
    private String cellLine;

    @TableField("disease")
    private String disease;

    // 构造函数
    public GoTermSummary() {}

    public GoTermSummary(String goTerm, String tissueCellType, String cellLine, String disease) {
        this.goTerm = goTerm;
        this.tissueCellType = tissueCellType;
        this.cellLine = cellLine;
        this.disease = disease;
    }

    // Getter和Setter方法
    public String getGoTerm() {
        return goTerm;
    }

    public void setGoTerm(String goTerm) {
        this.goTerm = goTerm;
    }

    public String getTissueCellType() {
        return tissueCellType;
    }

    public void setTissueCellType(String tissueCellType) {
        this.tissueCellType = tissueCellType;
    }

    public String getCellLine() {
        return cellLine;
    }

    public void setCellLine(String cellLine) {
        this.cellLine = cellLine;
    }

    public String getDisease() {
        return disease;
    }

    public void setDisease(String disease) {
        this.disease = disease;
    }

    @Override
    public String toString() {
        return "GoTermSummary{" +
                "goTerm='" + goTerm + '\'' +
                ", tissueCellType='" + tissueCellType + '\'' +
                ", cellLine='" + cellLine + '\'' +
                ", disease='" + disease + '\'' +
                '}';
    }
} 