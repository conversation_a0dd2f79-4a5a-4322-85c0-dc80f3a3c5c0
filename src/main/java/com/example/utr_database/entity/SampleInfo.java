package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sampleInfo")
public class SampleInfo {

    @TableId("sraAccession")
    private String sraAccession;

    @TableField("datasetId")
    private String datasetId;

    @TableField("geoAccession")
    private String geoAccession;

    @TableField("bioProjectId")
    private String bioProjectId;

    @TableField("bioSampleId")
    private String bioSampleId;

    @TableField("tissueCellType")
    private String tissueCellType;

    @TableField("cellLine")
    private String cellLine;

    @TableField("condition_")
    private String condition;

    @TableField("diseaseCategory")
    private String diseaseCategory;

    @TableField("dataType")
    private String dataType;

    @TableField("platform")
    private String platform;

    @TableField("instrument")
    private String instrument;

    @TableField("libraryLayout")
    private String libraryLayout;

    @TableField("detail")
    private String detail;

    @TableField("translatedTranscriptsNumber")
    private Integer translatedTranscriptsNumber;

    @TableField("translatedGenesNumber")
    private Integer translatedGenesNumber;
    
    // 手动添加所有getter和setter方法以确保JSON序列化正常
    public String getSraAccession() {
        return sraAccession;
    }

    public void setSraAccession(String sraAccession) {
        this.sraAccession = sraAccession;
    }

    public String getDatasetId() {
        return datasetId;
    }

    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    public String getGeoAccession() {
        return geoAccession;
    }

    public void setGeoAccession(String geoAccession) {
        this.geoAccession = geoAccession;
    }

    public String getBioProjectId() {
        return bioProjectId;
    }

    public void setBioProjectId(String bioProjectId) {
        this.bioProjectId = bioProjectId;
    }

    public String getBioSampleId() {
        return bioSampleId;
    }

    public void setBioSampleId(String bioSampleId) {
        this.bioSampleId = bioSampleId;
    }

    public String getTissueCellType() {
        return tissueCellType;
    }

    public void setTissueCellType(String tissueCellType) {
        this.tissueCellType = tissueCellType;
    }

    public String getCellLine() {
        return cellLine;
    }

    public void setCellLine(String cellLine) {
        this.cellLine = cellLine;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getDiseaseCategory() {
        return diseaseCategory;
    }

    public void setDiseaseCategory(String diseaseCategory) {
        this.diseaseCategory = diseaseCategory;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getInstrument() {
        return instrument;
    }

    public void setInstrument(String instrument) {
        this.instrument = instrument;
    }

    public String getLibraryLayout() {
        return libraryLayout;
    }

    public void setLibraryLayout(String libraryLayout) {
        this.libraryLayout = libraryLayout;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public Integer getTranslatedTranscriptsNumber() {
        return translatedTranscriptsNumber;
    }

    public void setTranslatedTranscriptsNumber(Integer translatedTranscriptsNumber) {
        this.translatedTranscriptsNumber = translatedTranscriptsNumber;
    }

    public Integer getTranslatedGenesNumber() {
        return translatedGenesNumber;
    }

    public void setTranslatedGenesNumber(Integer translatedGenesNumber) {
        this.translatedGenesNumber = translatedGenesNumber;
    }
}