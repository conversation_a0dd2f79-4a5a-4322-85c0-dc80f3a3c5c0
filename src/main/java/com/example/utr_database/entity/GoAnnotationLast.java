package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("goAnnotationLast")
public class GoAnnotationLast {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("geneSymbol")
    private String geneSymbol;

    @TableField("geneId")
    private String geneId;

    @TableField("goTerm")
    private String goTerm;

    @TableField("goDomain")
    private String goDomain;

    @TableField("goId")
    private String goId;

    @TableField("translationProjectIds")
    private String translationProjectIds;

    // 构造函数
    public GoAnnotationLast() {}

    // Getter和Setter方法
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGeneSymbol() {
        return geneSymbol;
    }

    public void setGeneSymbol(String geneSymbol) {
        this.geneSymbol = geneSymbol;
    }

    public String getGeneId() {
        return geneId;
    }

    public void setGeneId(String geneId) {
        this.geneId = geneId;
    }

    public String getGoTerm() {
        return goTerm;
    }

    public void setGoTerm(String goTerm) {
        this.goTerm = goTerm;
    }

    public String getGoDomain() {
        return goDomain;
    }

    public void setGoDomain(String goDomain) {
        this.goDomain = goDomain;
    }

    public String getGoId() {
        return goId;
    }

    public void setGoId(String goId) {
        this.goId = goId;
    }

    public String getTranslationProjectIds() {
        return translationProjectIds;
    }

    public void setTranslationProjectIds(String translationProjectIds) {
        this.translationProjectIds = translationProjectIds;
    }

    @Override
    public String toString() {
        return "GoAnnotationLast{" +
                "id=" + id +
                ", geneSymbol='" + geneSymbol + '\'' +
                ", geneId='" + geneId + '\'' +
                ", goTerm='" + goTerm + '\'' +
                ", goDomain='" + goDomain + '\'' +
                ", goId='" + goId + '\'' +
                ", translationProjectIds='" + translationProjectIds + '\'' +
                '}';
    }
}
