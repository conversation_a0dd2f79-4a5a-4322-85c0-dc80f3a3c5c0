package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("keggAnnotation")
public class KeggAnnotation {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("geneSymbol")
    private String geneSymbol;

    @TableField("geneId")
    private String geneId;

    @TableField("pathwayDescription")
    private String pathwayDescription;

    @TableField("pathwayId")
    private String pathwayId;

    @TableField("tissueCellType")
    private String tissueCellType;

    @TableField("cellLine")
    private String cellLine;

    @TableField("disease")
    private String disease;

    // 构造函数
    public KeggAnnotation() {}

    public KeggAnnotation(String geneSymbol, String geneId, String pathwayDescription, String pathwayId, 
                         String tissueCellType, String cellLine, String disease) {
        this.geneSymbol = geneSymbol;
        this.geneId = geneId;
        this.pathwayDescription = pathwayDescription;
        this.pathwayId = pathwayId;
        this.tissueCellType = tissueCellType;
        this.cellLine = cellLine;
        this.disease = disease;
    }

    // Getter和Setter方法
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGeneSymbol() {
        return geneSymbol;
    }

    public void setGeneSymbol(String geneSymbol) {
        this.geneSymbol = geneSymbol;
    }

    public String getGeneId() {
        return geneId;
    }

    public void setGeneId(String geneId) {
        this.geneId = geneId;
    }

    public String getPathwayDescription() {
        return pathwayDescription;
    }

    public void setPathwayDescription(String pathwayDescription) {
        this.pathwayDescription = pathwayDescription;
    }

    public String getPathwayId() {
        return pathwayId;
    }

    public void setPathwayId(String pathwayId) {
        this.pathwayId = pathwayId;
    }

    public String getTissueCellType() {
        return tissueCellType;
    }

    public void setTissueCellType(String tissueCellType) {
        this.tissueCellType = tissueCellType;
    }

    public String getCellLine() {
        return cellLine;
    }

    public void setCellLine(String cellLine) {
        this.cellLine = cellLine;
    }

    public String getDisease() {
        return disease;
    }

    public void setDisease(String disease) {
        this.disease = disease;
    }

    @Override
    public String toString() {
        return "KeggAnnotation{" +
                "id=" + id +
                ", geneSymbol='" + geneSymbol + '\'' +
                ", geneId='" + geneId + '\'' +
                ", pathwayDescription='" + pathwayDescription + '\'' +
                ", pathwayId='" + pathwayId + '\'' +
                ", tissueCellType='" + tissueCellType + '\'' +
                ", cellLine='" + cellLine + '\'' +
                ", disease='" + disease + '\'' +
                '}';
    }
} 