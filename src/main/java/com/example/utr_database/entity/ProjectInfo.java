package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("projectInfo")
public class ProjectInfo {
    
    @TableId("projectId")
    private String projectId;
    
    @TableField("bioProjectId")
    private String bioProjectId;
    
    @TableField("geoAccession")
    private String geoAccession;

    @TableField("title")
    private String title;

    @TableField("strategy")
    private String strategy;
    
    @TableField("tissueOrCellType")
    private String tissueOrCellType;
    
    @TableField("cellLine")
    private String cellLine;
    
    @TableField("disease")
    private String disease;

    @TableField("diseaseCategory")
    private String diseaseCategory;

    @TableField("srrNumber")
    private Integer srrNumber;
    
    @TableField("pmid")
    private String pmid;
    
    @TableField("translatedTranscriptsNumber")
    private Integer translatedTranscriptsNumber;

    @TableField("translatedGenesNumber")
    private Integer translatedGenesNumber;
    
    // 手动添加所有getter和setter方法以确保JSON序列化正常
    public String getProjectId() {
        return projectId;
    }
    
    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
    
    public String getBioProjectId() {
        return bioProjectId;
    }
    
    public void setBioProjectId(String bioProjectId) {
        this.bioProjectId = bioProjectId;
    }
    
    public String getGeoAccession() {
        return geoAccession;
    }
    
    public void setGeoAccession(String geoAccession) {
        this.geoAccession = geoAccession;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStrategy() {
        return strategy;
    }
    
    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }
    
    public String getTissueOrCellType() {
        return tissueOrCellType;
    }
    
    public void setTissueOrCellType(String tissueOrCellType) {
        this.tissueOrCellType = tissueOrCellType;
    }
    
    public String getCellLine() {
        return cellLine;
    }
    
    public void setCellLine(String cellLine) {
        this.cellLine = cellLine;
    }
    
    public String getDisease() {
        return disease;
    }
    
    public void setDisease(String disease) {
        this.disease = disease;
    }

    public String getDiseaseCategory() {
        return diseaseCategory;
    }

    public void setDiseaseCategory(String diseaseCategory) {
        this.diseaseCategory = diseaseCategory;
    }

    public Integer getSrrNumber() {
        return srrNumber;
    }
    
    public void setSrrNumber(Integer srrNumber) {
        this.srrNumber = srrNumber;
    }
    
    public String getPmid() {
        return pmid;
    }
    
    public void setPmid(String pmid) {
        this.pmid = pmid;
    }
    
    public Integer getTranslatedTranscriptsNumber() {
        return translatedTranscriptsNumber;
    }
    
    public void setTranslatedTranscriptsNumber(Integer translatedTranscriptsNumber) {
        this.translatedTranscriptsNumber = translatedTranscriptsNumber;
    }

    public Integer getTranslatedGenesNumber() {
        return translatedGenesNumber;
    }

    public void setTranslatedGenesNumber(Integer translatedGenesNumber) {
        this.translatedGenesNumber = translatedGenesNumber;
    }
}