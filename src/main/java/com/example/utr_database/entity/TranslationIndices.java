package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("translationIndices")
public class TranslationIndices {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    @TableField("transcriptId")
    private String transcriptId;
    
    @TableField("projectId")
    private String projectId;
    
    @TableField("bioprojectId")
    private String bioprojectId;
    
    @TableField("tissueCellType")
    private String tissueCellType;
    
    @TableField("cellLine")
    private String cellLine;
    
    @TableField("disease")
    private String disease;
    
    @TableField("tr")
    private BigDecimal tr;
    
    @TableField("evi")
    private BigDecimal evi;
    
    @TableField("te")
    private BigDecimal te;
    
    @TableField("geneId")
    private String geneId;
    
    @TableField("geneSymbol")
    private String geneSymbol;
    
    @TableField("threeUtrComp")
    private String threeUtrComp;
    
    @TableField("fiveUtrComp")
    private String fiveUtrComp;
    
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getTranscriptId() {
        return transcriptId;
    }
    
    public void setTranscriptId(String transcriptId) {
        this.transcriptId = transcriptId;
    }
    
    public String getProjectId() {
        return projectId;
    }
    
    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
    
    public String getBioprojectId() {
        return bioprojectId;
    }
    
    public void setBioprojectId(String bioprojectId) {
        this.bioprojectId = bioprojectId;
    }
    
    public String getTissueCellType() {
        return tissueCellType;
    }
    
    public void setTissueCellType(String tissueCellType) {
        this.tissueCellType = tissueCellType;
    }
    
    public String getCellLine() {
        return cellLine;
    }
    
    public void setCellLine(String cellLine) {
        this.cellLine = cellLine;
    }
    
    public String getDisease() {
        return disease;
    }
    
    public void setDisease(String disease) {
        this.disease = disease;
    }
    
    public BigDecimal getTr() {
        return tr;
    }
    
    public void setTr(BigDecimal tr) {
        this.tr = tr;
    }
    
    public BigDecimal getEvi() {
        return evi;
    }
    
    public void setEvi(BigDecimal evi) {
        this.evi = evi;
    }
    
    public BigDecimal getTe() {
        return te;
    }
    
    public void setTe(BigDecimal te) {
        this.te = te;
    }
    
    public String getGeneId() {
        return geneId;
    }
    
    public void setGeneId(String geneId) {
        this.geneId = geneId;
    }
    
    public String getGeneSymbol() {
        return geneSymbol;
    }
    
    public void setGeneSymbol(String geneSymbol) {
        this.geneSymbol = geneSymbol;
    }
    
    public String getThreeUtrComp() {
        return threeUtrComp;
    }
    
    public void setThreeUtrComp(String threeUtrComp) {
        this.threeUtrComp = threeUtrComp;
    }
    
    public String getFiveUtrComp() {
        return fiveUtrComp;
    }
    
    public void setFiveUtrComp(String fiveUtrComp) {
        this.fiveUtrComp = fiveUtrComp;
    }
} 