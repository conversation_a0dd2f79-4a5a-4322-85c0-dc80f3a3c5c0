package com.example.utr_database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("referenceInfo")
public class ReferenceInfo {
    @TableId("bioProjectId")
    private String bioProjectId;

    @TableField("geoAccession")
    private String geoAccession;

    @TableField("reference")
    private String reference;

    @TableField("pubmedId")
    private String pubmedId;

    @TableField("doi")
    private String doi;
    
    // 手动添加所有getter和setter方法以确保JSON序列化正常
    public String getBioProjectId() {
        return bioProjectId;
    }
    
    public void setBioProjectId(String bioProjectId) {
        this.bioProjectId = bioProjectId;
    }
    
    public String getGeoAccession() {
        return geoAccession;
    }
    
    public void setGeoAccession(String geoAccession) {
        this.geoAccession = geoAccession;
    }
    
    public String getReference() {
        return reference;
    }
    
    public void setReference(String reference) {
        this.reference = reference;
    }
    
    public String getPubmedId() {
        return pubmedId;
    }
    
    public void setPubmedId(String pubmedId) {
        this.pubmedId = pubmedId;
    }
    
    public String getDoi() {
        return doi;
    }
    
    public void setDoi(String doi) {
        this.doi = doi;
    }
} 