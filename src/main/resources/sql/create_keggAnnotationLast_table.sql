-- 创建keggAnnotationLast表
CREATE TABLE IF NOT EXISTS keggAnnotationLast (
    id INT AUTO_INCREMENT PRIMARY KEY,
    geneSymbol VARCHAR(100),
    geneId VARCHAR(100),
    pathwayDescription VARCHAR(500),
    pathwayId VARCHAR(20),
    translationProjectIds TEXT,
    INDEX idx_gene_symbol (geneSymbol),
    INDEX idx_gene_id (geneId),
    INDEX idx_pathway_desc (pathwayDescription(255)),
    INDEX idx_pathway_id (pathwayId),
    INDEX idx_project_ids (translationProjectIds(255)),
    FULLTEXT INDEX ft_kegg_search (geneSymbol, geneId, pathwayDescription, pathwayId, translationProjectIds)
);
