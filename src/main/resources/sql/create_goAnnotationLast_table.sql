-- 创建goAnnotationLast表
CREATE TABLE IF NOT EXISTS goAnnotationLast (
    id INT AUTO_INCREMENT PRIMARY KEY,
    geneSymbol VARCHAR(100),
    geneId VARCHAR(100),
    goTerm VARCHAR(500),
    goDomain VARCHAR(10),
    goId VARCHAR(20),
    translationProjectIds TEXT,
    INDEX idx_gene_symbol (geneSymbol),
    INDEX idx_gene_id (geneId),
    INDEX idx_go_term (goTerm(255)),
    INDEX idx_go_domain (goDomain),
    INDEX idx_go_id (goId),
    INDEX idx_project_ids (translationProjectIds(255)),
    FULLTEXT INDEX ft_go_search (geneSymbol, geneId, goTerm, goId, translationProjectIds)
);
