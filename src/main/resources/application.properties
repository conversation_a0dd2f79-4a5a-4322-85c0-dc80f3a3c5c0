spring.application.name=TEDD_Database
# 基础配置
server.port=8081
server.address=0.0.0.0
server.tomcat.uri-encoding=UTF-8
# 数据库配置
spring.datasource.url=jdbc:mysql://**************:3306/utr_database?useSSL=false&serverTimezone=UTC&characterEncoding=UTF-8&allowPublicKeyRetrieval=true&useUnicode=true&jdbcCompliantTruncation=false&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&noAccessToProcedureBodies=true
spring.datasource.username=luty
spring.datasource.password=vb70e57o7U!G
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver


# MyBatis-Plus配置
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.type-aliases-package=com.example.utr_database.entity
# mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.configuration.auto-mapping-behavior=partial
mybatis-plus.configuration.call-setters-on-nulls=true
mybatis-plus.configuration.jdbc-type-for-null=NULL
mybatis-plus.configuration.default-fetch-size=100
mybatis-plus.configuration.default-statement-timeout=30

# API路径配置
spring.data.rest.base-path=/tedd/api
spring.mvc.servlet.path=${spring.data.rest.base-path}

# Swagger配置
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.path=/api-docs

# 静态资源配置
spring.mvc.static-path-pattern=/**
spring.web.resources.static-locations=classpath:/static/

# 字符编码配置
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true

# 日志配置
# 开启所有相关的DEBUG日志以便于问题排查
logging.level.com.example.utr_database=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.jdbc=DEBUG
logging.level.com.example.utr_database.mapper=DEBUG
logging.level.org.apache.ibatis=DEBUG
logging.level.java.sql=DEBUG

# 错误信息详细输出
# 在开发和调试阶段，开启详细错误信息有助于快速定位问题
server.error.include-message=always
server.error.include-stacktrace=always
server.error.include-exception=true