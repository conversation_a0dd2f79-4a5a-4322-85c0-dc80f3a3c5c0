package com.example.utr_database.service;

import com.example.utr_database.entity.GoAnnotationLast;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class GoAnnotationLastServiceTest {

    @Autowired(required = false)
    private GoAnnotationLastService goAnnotationLastService;

    @Test
    public void testServiceInjection() {
        // 测试服务注入是否成功
        assertNotNull(goAnnotationLastService);
    }

    @Test
    public void testGetAllGoAnnotationLasts() {
        // 测试获取所有GO注释信息
        if (goAnnotationLastService != null) {
            List<GoAnnotationLast> result = goAnnotationLastService.getAllGoAnnotationLasts();
            assertNotNull(result);
        }
    }

    @Test
    public void testGetByGoTerm() {
        // 测试根据goTerm获取数据的核心功能
        String testGoTerm = "test_go_term";
        List<GoAnnotationLast> result = goAnnotationLastService.getByGoTerm(testGoTerm);
        assertNotNull(result);
        // 验证返回的结果都包含指定的goTerm
        for (GoAnnotationLast annotation : result) {
            assertTrue(annotation.getGoTerm() != null && 
                      annotation.getGoTerm().contains(testGoTerm));
        }
    }

    @Test
    public void testGetByGeneSymbol() {
        // 测试根据geneSymbol获取数据
        String testGeneSymbol = "test_gene";
        List<GoAnnotationLast> result = goAnnotationLastService.getByGeneSymbol(testGeneSymbol);
        assertNotNull(result);
    }

    @Test
    public void testGetByGeneId() {
        // 测试根据geneId获取数据
        String testGeneId = "test_gene_id";
        List<GoAnnotationLast> result = goAnnotationLastService.getByGeneId(testGeneId);
        assertNotNull(result);
    }

    @Test
    public void testGetByGoId() {
        // 测试根据goId获取数据
        String testGoId = "GO:0000001";
        List<GoAnnotationLast> result = goAnnotationLastService.getByGoId(testGoId);
        assertNotNull(result);
    }

    @Test
    public void testGetByGoDomain() {
        // 测试根据goDomain获取数据
        String testGoDomain = "BP";
        List<GoAnnotationLast> result = goAnnotationLastService.getByGoDomain(testGoDomain);
        assertNotNull(result);
    }

    @Test
    public void testGetByTranslationProjectIds() {
        // 测试根据translationProjectIds获取数据
        String testProjectIds = "project1";
        List<GoAnnotationLast> result = goAnnotationLastService.getByTranslationProjectIds(testProjectIds);
        assertNotNull(result);
    }

    @Test
    public void testSearchGoAnnotationLasts() {
        // 测试全文搜索功能
        String keyword = "test";
        List<GoAnnotationLast> result = goAnnotationLastService.searchGoAnnotationLasts(keyword);
        assertNotNull(result);
    }
}
